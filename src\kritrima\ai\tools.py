"""
Function tool calling schema and registry for AI providers.

Provides a comprehensive set of tools for shell commands, file operations,
and system interactions that can be used by AI agents.
"""

import json
import asyncio
from typing import Any, Dict, List, Optional, Callable, Union, Type
from dataclasses import dataclass, field
from abc import ABC, abstractmethod
from enum import Enum

from pydantic import BaseModel, Field


class ToolType(str, Enum):
    """Types of tools available."""
    SHELL = "shell"
    FILE = "file"
    SYSTEM = "system"
    NETWORK = "network"
    GIT = "git"
    PACKAGE = "package"


@dataclass
class ToolParameter:
    """Represents a tool parameter."""
    name: str
    type: str
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[str]] = None


@dataclass
class ToolSchema:
    """Schema definition for a tool."""
    name: str
    description: str
    parameters: List[ToolParameter]
    tool_type: ToolType
    dangerous: bool = False
    requires_approval: bool = True
    
    def to_openai_schema(self) -> Dict[str, Any]:
        """Convert to OpenAI function calling schema."""
        properties = {}
        required = []
        
        for param in self.parameters:
            prop = {
                "type": param.type,
                "description": param.description
            }
            
            if param.enum:
                prop["enum"] = param.enum
            
            if param.default is not None:
                prop["default"] = param.default
            
            properties[param.name] = prop
            
            if param.required:
                required.append(param.name)
        
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": properties,
                    "required": required
                }
            }
        }


class Tool(ABC):
    """Abstract base class for tools."""
    
    def __init__(self, schema: ToolSchema):
        """Initialize tool with schema."""
        self.schema = schema
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """
        Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool parameters
            
        Returns:
            Dictionary with execution results
        """
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and process tool parameters.
        
        Args:
            parameters: Raw parameters from AI
            
        Returns:
            Validated and processed parameters
            
        Raises:
            ValueError: If parameters are invalid
        """
        validated = {}
        
        for param in self.schema.parameters:
            value = parameters.get(param.name)
            
            # Check required parameters
            if param.required and value is None:
                if param.default is not None:
                    value = param.default
                else:
                    raise ValueError(f"Required parameter '{param.name}' is missing")
            
            # Type validation (basic)
            if value is not None:
                if param.type == "string" and not isinstance(value, str):
                    value = str(value)
                elif param.type == "integer" and not isinstance(value, int):
                    try:
                        value = int(value)
                    except (ValueError, TypeError):
                        raise ValueError(f"Parameter '{param.name}' must be an integer")
                elif param.type == "boolean" and not isinstance(value, bool):
                    if isinstance(value, str):
                        value = value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        value = bool(value)
                elif param.type == "array" and not isinstance(value, list):
                    if isinstance(value, str):
                        try:
                            value = json.loads(value)
                        except json.JSONDecodeError:
                            value = [value]
                    else:
                        value = [value]
                
                # Enum validation
                if param.enum and value not in param.enum:
                    raise ValueError(f"Parameter '{param.name}' must be one of: {param.enum}")
            
            validated[param.name] = value
        
        return validated


class ShellTool(Tool):
    """Tool for executing shell commands."""
    
    def __init__(self):
        """Initialize shell tool."""
        schema = ToolSchema(
            name="shell",
            description="Execute shell commands in the local environment",
            parameters=[
                ToolParameter(
                    name="command",
                    type="string",
                    description="The shell command to execute",
                    required=True
                ),
                ToolParameter(
                    name="working_directory",
                    type="string",
                    description="Working directory for the command (optional)",
                    required=False
                ),
                ToolParameter(
                    name="timeout",
                    type="integer",
                    description="Command timeout in seconds (default: 30)",
                    required=False,
                    default=30
                ),
                ToolParameter(
                    name="capture_output",
                    type="boolean",
                    description="Whether to capture command output (default: true)",
                    required=False,
                    default=True
                )
            ],
            tool_type=ToolType.SHELL,
            dangerous=True,
            requires_approval=True
        )
        super().__init__(schema)
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute shell command."""
        from ..execution.shell_executor import ShellExecutor
        
        params = self.validate_parameters(kwargs)
        executor = ShellExecutor()
        
        result = await executor.execute(
            command=params["command"],
            working_directory=params.get("working_directory"),
            timeout=params.get("timeout", 30),
            capture_output=params.get("capture_output", True)
        )
        
        return {
            "success": result.success,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.return_code,
            "execution_time": result.execution_time
        }


class FileReadTool(Tool):
    """Tool for reading files."""
    
    def __init__(self):
        """Initialize file read tool."""
        schema = ToolSchema(
            name="file_read",
            description="Read the contents of a file",
            parameters=[
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="Path to the file to read",
                    required=True
                ),
                ToolParameter(
                    name="encoding",
                    type="string",
                    description="File encoding (default: utf-8)",
                    required=False,
                    default="utf-8"
                ),
                ToolParameter(
                    name="max_size",
                    type="integer",
                    description="Maximum file size to read in bytes (default: 1MB)",
                    required=False,
                    default=1024*1024
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=False,
            requires_approval=False
        )
        super().__init__(schema)
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Read file contents."""
        from ..execution.file_operations import FileOperations
        
        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()
        
        result = await file_ops.read_file(
            file_path=params["file_path"],
            encoding=params.get("encoding", "utf-8"),
            max_size=params.get("max_size", 1024*1024)
        )
        
        return {
            "success": result.success,
            "content": result.content,
            "error": result.error,
            "file_size": result.file_size,
            "encoding": result.encoding
        }


class FileWriteTool(Tool):
    """Tool for writing files."""
    
    def __init__(self):
        """Initialize file write tool."""
        schema = ToolSchema(
            name="file_write",
            description="Write content to a file",
            parameters=[
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="Path to the file to write",
                    required=True
                ),
                ToolParameter(
                    name="content",
                    type="string",
                    description="Content to write to the file",
                    required=True
                ),
                ToolParameter(
                    name="encoding",
                    type="string",
                    description="File encoding (default: utf-8)",
                    required=False,
                    default="utf-8"
                ),
                ToolParameter(
                    name="mode",
                    type="string",
                    description="Write mode: 'write' (overwrite) or 'append'",
                    required=False,
                    default="write",
                    enum=["write", "append"]
                ),
                ToolParameter(
                    name="create_directories",
                    type="boolean",
                    description="Create parent directories if they don't exist",
                    required=False,
                    default=True
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=True,
            requires_approval=True
        )
        super().__init__(schema)
    
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Write file contents."""
        from ..execution.file_operations import FileOperations
        
        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()
        
        result = await file_ops.write_file(
            file_path=params["file_path"],
            content=params["content"],
            encoding=params.get("encoding", "utf-8"),
            mode=params.get("mode", "write"),
            create_directories=params.get("create_directories", True)
        )
        
        return {
            "success": result.success,
            "error": result.error,
            "bytes_written": result.bytes_written
        }


class FileSearchTool(Tool):
    """Tool for searching within files."""

    def __init__(self):
        """Initialize file search tool."""
        schema = ToolSchema(
            name="file_search",
            description="Search for patterns within files using grep-like functionality",
            parameters=[
                ToolParameter(
                    name="pattern",
                    type="string",
                    description="Search pattern (regex supported)",
                    required=True
                ),
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="Path to search in (file or directory)",
                    required=True
                ),
                ToolParameter(
                    name="recursive",
                    type="boolean",
                    description="Search recursively in directories",
                    required=False,
                    default=False
                ),
                ToolParameter(
                    name="case_sensitive",
                    type="boolean",
                    description="Case sensitive search",
                    required=False,
                    default=True
                ),
                ToolParameter(
                    name="max_results",
                    type="integer",
                    description="Maximum number of results to return",
                    required=False,
                    default=100
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=False,
            requires_approval=False
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Search within files."""
        from ..execution.file_operations import FileOperations

        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()

        result = await file_ops.search_files(
            pattern=params["pattern"],
            file_path=params["file_path"],
            recursive=params.get("recursive", False),
            case_sensitive=params.get("case_sensitive", True),
            max_results=params.get("max_results", 100)
        )

        return {
            "success": result.success,
            "matches": result.matches,
            "total_matches": result.total_matches,
            "files_searched": result.files_searched,
            "error": result.error
        }


class FileCopyTool(Tool):
    """Tool for copying files."""

    def __init__(self):
        """Initialize file copy tool."""
        schema = ToolSchema(
            name="file_copy",
            description="Copy a file to another location",
            parameters=[
                ToolParameter(
                    name="source_path",
                    type="string",
                    description="Source file path",
                    required=True
                ),
                ToolParameter(
                    name="destination_path",
                    type="string",
                    description="Destination file path",
                    required=True
                ),
                ToolParameter(
                    name="overwrite",
                    type="boolean",
                    description="Whether to overwrite existing files",
                    required=False,
                    default=False
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=True,
            requires_approval=True
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Copy file."""
        from ..execution.file_operations import FileOperations

        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()

        result = await file_ops.copy_file(
            source_path=params["source_path"],
            destination_path=params["destination_path"],
            overwrite=params.get("overwrite", False)
        )

        return {
            "success": result.success,
            "error": result.error,
            "file_size": result.file_size
        }


class FileMoveTool(Tool):
    """Tool for moving files."""

    def __init__(self):
        """Initialize file move tool."""
        schema = ToolSchema(
            name="file_move",
            description="Move a file to another location",
            parameters=[
                ToolParameter(
                    name="source_path",
                    type="string",
                    description="Source file path",
                    required=True
                ),
                ToolParameter(
                    name="destination_path",
                    type="string",
                    description="Destination file path",
                    required=True
                ),
                ToolParameter(
                    name="overwrite",
                    type="boolean",
                    description="Whether to overwrite existing files",
                    required=False,
                    default=False
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=True,
            requires_approval=True
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Move file."""
        from ..execution.file_operations import FileOperations

        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()

        result = await file_ops.move_file(
            source_path=params["source_path"],
            destination_path=params["destination_path"],
            overwrite=params.get("overwrite", False)
        )

        return {
            "success": result.success,
            "error": result.error
        }


class FileDeleteTool(Tool):
    """Tool for deleting files."""

    def __init__(self):
        """Initialize file delete tool."""
        schema = ToolSchema(
            name="file_delete",
            description="Delete a file",
            parameters=[
                ToolParameter(
                    name="file_path",
                    type="string",
                    description="Path to the file to delete",
                    required=True
                ),
                ToolParameter(
                    name="force",
                    type="boolean",
                    description="Force deletion even if file is read-only",
                    required=False,
                    default=False
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=True,
            requires_approval=True
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Delete file."""
        from ..execution.file_operations import FileOperations

        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()

        result = await file_ops.delete_file(
            file_path=params["file_path"],
            force=params.get("force", False)
        )

        return {
            "success": result.success,
            "error": result.error
        }


class DirectoryListTool(Tool):
    """Tool for listing directory contents."""

    def __init__(self):
        """Initialize directory list tool."""
        schema = ToolSchema(
            name="directory_list",
            description="List directory contents",
            parameters=[
                ToolParameter(
                    name="directory_path",
                    type="string",
                    description="Path to the directory",
                    required=True
                ),
                ToolParameter(
                    name="recursive",
                    type="boolean",
                    description="List recursively",
                    required=False,
                    default=False
                ),
                ToolParameter(
                    name="include_hidden",
                    type="boolean",
                    description="Include hidden files",
                    required=False,
                    default=False
                ),
                ToolParameter(
                    name="pattern",
                    type="string",
                    description="Glob pattern to filter files",
                    required=False
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=False,
            requires_approval=False
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """List directory contents."""
        from ..execution.file_operations import FileOperations

        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()

        result = await file_ops.list_directory(
            directory_path=params["directory_path"],
            recursive=params.get("recursive", False),
            include_hidden=params.get("include_hidden", False),
            pattern=params.get("pattern")
        )

        return {
            "success": result.success,
            "files_found": result.files_found,
            "error": result.error
        }


class DirectoryCreateTool(Tool):
    """Tool for creating directories."""

    def __init__(self):
        """Initialize directory create tool."""
        schema = ToolSchema(
            name="directory_create",
            description="Create a directory",
            parameters=[
                ToolParameter(
                    name="directory_path",
                    type="string",
                    description="Path to the directory to create",
                    required=True
                ),
                ToolParameter(
                    name="parents",
                    type="boolean",
                    description="Create parent directories if needed",
                    required=False,
                    default=True
                ),
                ToolParameter(
                    name="exist_ok",
                    type="boolean",
                    description="Don't raise error if directory exists",
                    required=False,
                    default=True
                )
            ],
            tool_type=ToolType.FILE,
            dangerous=False,
            requires_approval=True
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Create directory."""
        from ..execution.file_operations import FileOperations

        params = self.validate_parameters(kwargs)
        file_ops = FileOperations()

        result = await file_ops.create_directory(
            directory_path=params["directory_path"],
            parents=params.get("parents", True),
            exist_ok=params.get("exist_ok", True)
        )

        return {
            "success": result.success,
            "error": result.error
        }


class SystemInfoTool(Tool):
    """Tool for getting system information."""

    def __init__(self):
        """Initialize system info tool."""
        schema = ToolSchema(
            name="system_info",
            description="Get system information including OS, CPU, memory, and disk usage",
            parameters=[
                ToolParameter(
                    name="info_type",
                    type="string",
                    description="Type of information to get",
                    required=False,
                    default="all",
                    enum=["all", "os", "cpu", "memory", "disk", "network"]
                )
            ],
            tool_type=ToolType.SYSTEM,
            dangerous=False,
            requires_approval=False
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """Get system information."""
        import platform
        import psutil
        import socket
        from datetime import datetime

        params = self.validate_parameters(kwargs)
        info_type = params.get("info_type", "all")

        try:
            system_info = {}

            if info_type in ["all", "os"]:
                system_info["os"] = {
                    "system": platform.system(),
                    "release": platform.release(),
                    "version": platform.version(),
                    "machine": platform.machine(),
                    "processor": platform.processor(),
                    "architecture": platform.architecture(),
                    "hostname": socket.gethostname(),
                    "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat()
                }

            if info_type in ["all", "cpu"]:
                system_info["cpu"] = {
                    "physical_cores": psutil.cpu_count(logical=False),
                    "total_cores": psutil.cpu_count(logical=True),
                    "max_frequency": psutil.cpu_freq().max if psutil.cpu_freq() else None,
                    "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None,
                    "cpu_usage": psutil.cpu_percent(interval=1)
                }

            if info_type in ["all", "memory"]:
                memory = psutil.virtual_memory()
                swap = psutil.swap_memory()
                system_info["memory"] = {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percentage": memory.percent,
                    "swap_total": swap.total,
                    "swap_used": swap.used,
                    "swap_percentage": swap.percent
                }

            if info_type in ["all", "disk"]:
                disk_usage = psutil.disk_usage('/')
                system_info["disk"] = {
                    "total": disk_usage.total,
                    "used": disk_usage.used,
                    "free": disk_usage.free,
                    "percentage": (disk_usage.used / disk_usage.total) * 100
                }

            if info_type in ["all", "network"]:
                network_stats = psutil.net_io_counters()
                system_info["network"] = {
                    "bytes_sent": network_stats.bytes_sent,
                    "bytes_received": network_stats.bytes_recv,
                    "packets_sent": network_stats.packets_sent,
                    "packets_received": network_stats.packets_recv
                }

            return {
                "success": True,
                "system_info": system_info,
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "system_info": None,
                "error": str(e)
            }


class ProcessListTool(Tool):
    """Tool for listing running processes."""

    def __init__(self):
        """Initialize process list tool."""
        schema = ToolSchema(
            name="process_list",
            description="List running processes with details",
            parameters=[
                ToolParameter(
                    name="limit",
                    type="integer",
                    description="Maximum number of processes to return",
                    required=False,
                    default=20
                ),
                ToolParameter(
                    name="sort_by",
                    type="string",
                    description="Sort processes by field",
                    required=False,
                    default="cpu_percent",
                    enum=["pid", "name", "cpu_percent", "memory_percent", "create_time"]
                )
            ],
            tool_type=ToolType.SYSTEM,
            dangerous=False,
            requires_approval=False
        )
        super().__init__(schema)

    async def execute(self, **kwargs) -> Dict[str, Any]:
        """List running processes."""
        import psutil
        from datetime import datetime

        params = self.validate_parameters(kwargs)
        limit = params.get("limit", 20)
        sort_by = params.get("sort_by", "cpu_percent")

        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'create_time', 'status']):
                try:
                    proc_info = proc.info
                    proc_info['create_time'] = datetime.fromtimestamp(proc_info['create_time']).isoformat()
                    processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

            # Sort processes
            reverse = sort_by in ["cpu_percent", "memory_percent"]
            processes.sort(key=lambda x: x.get(sort_by, 0), reverse=reverse)

            # Limit results
            processes = processes[:limit]

            return {
                "success": True,
                "processes": processes,
                "total_processes": len(processes),
                "error": None
            }

        except Exception as e:
            return {
                "success": False,
                "processes": None,
                "total_processes": 0,
                "error": str(e)
            }


class ToolRegistry:
    """
    Registry for managing available tools.

    Provides centralized management of tools, including registration,
    discovery, and execution with safety checks.
    """

    def __init__(self):
        """Initialize tool registry."""
        self.tools: Dict[str, Tool] = {}
        self._register_default_tools()

    def _register_default_tools(self) -> None:
        """Register default tools."""
        default_tools = [
            ShellTool(),
            FileReadTool(),
            FileWriteTool(),
            FileSearchTool(),
            FileCopyTool(),
            FileMoveTool(),
            FileDeleteTool(),
            DirectoryListTool(),
            DirectoryCreateTool(),
            SystemInfoTool(),
            ProcessListTool(),
        ]

        for tool in default_tools:
            self.register_tool(tool)

    def register_tool(self, tool: Tool) -> None:
        """
        Register a tool.

        Args:
            tool: Tool instance to register
        """
        self.tools[tool.schema.name] = tool

    def unregister_tool(self, tool_name: str) -> bool:
        """
        Unregister a tool.

        Args:
            tool_name: Name of the tool to unregister

        Returns:
            True if tool was unregistered, False if not found
        """
        return self.tools.pop(tool_name, None) is not None

    def get_tool(self, tool_name: str) -> Optional[Tool]:
        """
        Get a tool by name.

        Args:
            tool_name: Name of the tool

        Returns:
            Tool instance or None if not found
        """
        return self.tools.get(tool_name)

    def list_tools(self, tool_type: Optional[ToolType] = None) -> List[str]:
        """
        List available tools.

        Args:
            tool_type: Filter by tool type (optional)

        Returns:
            List of tool names
        """
        if tool_type:
            return [
                name for name, tool in self.tools.items()
                if tool.schema.tool_type == tool_type
            ]
        return list(self.tools.keys())

    def get_openai_schemas(self, tool_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        Get OpenAI function calling schemas for tools.

        Args:
            tool_names: Specific tools to include (optional, defaults to all)

        Returns:
            List of OpenAI function schemas
        """
        if tool_names:
            tools = [self.tools[name] for name in tool_names if name in self.tools]
        else:
            tools = list(self.tools.values())

        return [tool.schema.to_openai_schema() for tool in tools]

    async def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        approval_callback: Optional[Callable[[str, Dict[str, Any]], bool]] = None
    ) -> Dict[str, Any]:
        """
        Execute a tool with safety checks.

        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            approval_callback: Optional callback for approval (returns True to approve)

        Returns:
            Tool execution results

        Raises:
            ValueError: If tool not found or parameters invalid
            PermissionError: If tool execution not approved
        """
        tool = self.get_tool(tool_name)
        if not tool:
            raise ValueError(f"Tool '{tool_name}' not found")

        # Check if approval is required
        if tool.schema.requires_approval and approval_callback:
            approved = approval_callback(tool_name, parameters)
            if not approved:
                raise PermissionError(f"Execution of tool '{tool_name}' was not approved")

        try:
            result = await tool.execute(**parameters)
            return {
                "tool_name": tool_name,
                "success": True,
                "result": result,
                "error": None
            }
        except Exception as e:
            return {
                "tool_name": tool_name,
                "success": False,
                "result": None,
                "error": str(e)
            }

    def get_dangerous_tools(self) -> List[str]:
        """Get list of dangerous tools that require extra caution."""
        return [
            name for name, tool in self.tools.items()
            if tool.schema.dangerous
        ]

    def get_safe_tools(self) -> List[str]:
        """Get list of safe tools that don't require approval."""
        return [
            name for name, tool in self.tools.items()
            if not tool.schema.requires_approval
        ]


# Global tool registry instance
_registry: Optional[ToolRegistry] = None


def get_tool_registry() -> ToolRegistry:
    """Get the global tool registry instance."""
    global _registry
    if _registry is None:
        _registry = ToolRegistry()
    return _registry


def register_custom_tool(tool: Tool) -> None:
    """Register a custom tool with the global registry."""
    registry = get_tool_registry()
    registry.register_tool(tool)
