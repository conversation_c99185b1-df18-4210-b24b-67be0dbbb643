"""
Context manager for intelligent project discovery and environment awareness.

Automatically discovers project structure, dependencies, and environment details
to provide rich context to the AI assistant.
"""

import os
import sys
import subprocess
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple
from datetime import datetime, timezone

import git
from git.exc import InvalidGitRepositoryError

from ..session import ContextInfo


class ContextManager:
    """
    Intelligent context manager for project discovery and environment awareness.
    
    Automatically analyzes the current working directory to understand:
    - Project type and structure
    - Git repository information
    - Programming language versions
    - Package files and dependencies
    - Recent file activity
    - Environment variables
    """
    
    def __init__(self, working_directory: Path):
        """
        Initialize context manager.
        
        Args:
            working_directory: Directory to analyze for context
        """
        self.working_directory = Path(working_directory).resolve()
        self._cache: Optional[ContextInfo] = None
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = 300  # 5 minutes cache TTL
    
    def get_context(self, force_refresh: bool = False) -> ContextInfo:
        """
        Get comprehensive context information.
        
        Args:
            force_refresh: Force refresh of cached context
            
        Returns:
            ContextInfo with discovered information
        """
        # Check cache validity
        if (not force_refresh and 
            self._cache and 
            self._cache_timestamp and
            (datetime.now(timezone.utc) - self._cache_timestamp).total_seconds() < self._cache_ttl):
            return self._cache
        
        # Discover context
        context = ContextInfo(
            working_directory=self.working_directory,
            project_type=self._detect_project_type(),
            git_repository=self._get_git_info().get("repository"),
            git_branch=self._get_git_info().get("branch"),
            python_version=self._get_python_version(),
            node_version=self._get_node_version(),
            package_files=self._find_package_files(),
            recent_files=self._get_recent_files(),
            environment_variables=self._get_relevant_env_vars(),
            last_updated=datetime.now(timezone.utc)
        )
        
        # Update cache
        self._cache = context
        self._cache_timestamp = datetime.now(timezone.utc)
        
        return context
    
    def _detect_project_type(self) -> Optional[str]:
        """Detect the type of project in the working directory."""
        project_indicators = {
            "python": ["setup.py", "pyproject.toml", "requirements.txt", "Pipfile", "poetry.lock"],
            "node": ["package.json", "yarn.lock", "package-lock.json", "node_modules"],
            "rust": ["Cargo.toml", "Cargo.lock"],
            "go": ["go.mod", "go.sum"],
            "java": ["pom.xml", "build.gradle", "build.gradle.kts"],
            "dotnet": ["*.csproj", "*.sln", "*.fsproj", "*.vbproj"],
            "ruby": ["Gemfile", "Gemfile.lock", "*.gemspec"],
            "php": ["composer.json", "composer.lock"],
            "docker": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"],
            "terraform": ["*.tf", "terraform.tfstate"],
            "kubernetes": ["*.yaml", "*.yml"] # This is too broad, needs refinement
        }
        
        detected_types = []
        
        for project_type, indicators in project_indicators.items():
            for indicator in indicators:
                if indicator.startswith("*."):
                    # Glob pattern
                    extension = indicator[2:]
                    if any(f.suffix == f".{extension}" for f in self.working_directory.rglob("*")):
                        detected_types.append(project_type)
                        break
                else:
                    # Exact file/directory name
                    if (self.working_directory / indicator).exists():
                        detected_types.append(project_type)
                        break
        
        # Return the most specific type or the first detected
        if "docker" in detected_types:
            return "docker"
        elif "kubernetes" in detected_types and any(
            self._is_k8s_file(f) for f in self.working_directory.rglob("*.yaml")
        ):
            return "kubernetes"
        elif detected_types:
            return detected_types[0]
        
        return None
    
    def _is_k8s_file(self, file_path: Path) -> bool:
        """Check if a YAML file is a Kubernetes manifest."""
        try:
            import yaml
            with open(file_path, 'r') as f:
                content = yaml.safe_load(f)
                if isinstance(content, dict):
                    return "apiVersion" in content and "kind" in content
        except Exception:
            pass
        return False
    
    def _get_git_info(self) -> Dict[str, Optional[str]]:
        """Get Git repository information."""
        try:
            repo = git.Repo(self.working_directory, search_parent_directories=True)
            
            # Get remote URL
            remote_url = None
            if repo.remotes:
                remote_url = repo.remotes.origin.url
            
            return {
                "repository": remote_url,
                "branch": repo.active_branch.name if repo.active_branch else None,
                "commit": repo.head.commit.hexsha[:8] if repo.head.commit else None,
                "is_dirty": repo.is_dirty(),
                "untracked_files": len(repo.untracked_files)
            }
        except (InvalidGitRepositoryError, Exception):
            return {"repository": None, "branch": None}
    
    def _get_python_version(self) -> Optional[str]:
        """Get Python version information."""
        try:
            # Try current Python interpreter first
            version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            
            # Check if there's a specific Python version in project
            python_version_files = [
                ".python-version",  # pyenv
                "runtime.txt",      # Heroku
                "Pipfile"          # pipenv
            ]
            
            for version_file in python_version_files:
                file_path = self.working_directory / version_file
                if file_path.exists():
                    try:
                        content = file_path.read_text().strip()
                        if version_file == "runtime.txt" and content.startswith("python-"):
                            return content[7:]  # Remove "python-" prefix
                        elif version_file == ".python-version":
                            return content
                        elif version_file == "Pipfile":
                            # Parse Pipfile for python_version
                            import toml
                            pipfile_data = toml.load(file_path)
                            if "requires" in pipfile_data and "python_version" in pipfile_data["requires"]:
                                return pipfile_data["requires"]["python_version"]
                    except Exception:
                        continue
            
            return version
        except Exception:
            return None
    
    def _get_node_version(self) -> Optional[str]:
        """Get Node.js version information."""
        try:
            # Check for .nvmrc file
            nvmrc_path = self.working_directory / ".nvmrc"
            if nvmrc_path.exists():
                return nvmrc_path.read_text().strip()
            
            # Check package.json for engines
            package_json_path = self.working_directory / "package.json"
            if package_json_path.exists():
                try:
                    with open(package_json_path, 'r') as f:
                        package_data = json.load(f)
                        if "engines" in package_data and "node" in package_data["engines"]:
                            return package_data["engines"]["node"]
                except Exception:
                    pass
            
            # Try to get current Node version
            result = subprocess.run(
                ["node", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                return result.stdout.strip().lstrip('v')
        except Exception:
            pass
        
        return None
    
    def _find_package_files(self) -> List[str]:
        """Find package/dependency files in the project."""
        package_files = [
            "package.json", "package-lock.json", "yarn.lock",
            "requirements.txt", "Pipfile", "Pipfile.lock", "poetry.lock", "pyproject.toml",
            "Cargo.toml", "Cargo.lock",
            "go.mod", "go.sum",
            "pom.xml", "build.gradle", "build.gradle.kts",
            "Gemfile", "Gemfile.lock",
            "composer.json", "composer.lock",
            "setup.py", "setup.cfg"
        ]
        
        found_files = []
        for file_name in package_files:
            file_path = self.working_directory / file_name
            if file_path.exists():
                found_files.append(file_name)
        
        return found_files
    
    def _get_recent_files(self, limit: int = 20) -> List[str]:
        """Get list of recently modified files."""
        try:
            files_with_mtime = []
            
            # Get all files (excluding hidden and common ignore patterns)
            ignore_patterns = {
                ".git", "__pycache__", "node_modules", ".venv", "venv",
                ".pytest_cache", ".mypy_cache", "dist", "build",
                ".DS_Store", "Thumbs.db"
            }
            
            for file_path in self.working_directory.rglob("*"):
                if file_path.is_file():
                    # Skip ignored patterns
                    if any(pattern in str(file_path) for pattern in ignore_patterns):
                        continue
                    
                    # Skip hidden files
                    if any(part.startswith('.') for part in file_path.parts):
                        continue
                    
                    try:
                        mtime = file_path.stat().st_mtime
                        relative_path = file_path.relative_to(self.working_directory)
                        files_with_mtime.append((str(relative_path), mtime))
                    except (OSError, ValueError):
                        continue
            
            # Sort by modification time (most recent first) and return top N
            files_with_mtime.sort(key=lambda x: x[1], reverse=True)
            return [file_path for file_path, _ in files_with_mtime[:limit]]
        
        except Exception:
            return []
    
    def _get_relevant_env_vars(self) -> Dict[str, str]:
        """Get relevant environment variables."""
        relevant_vars = [
            # Development
            "NODE_ENV", "PYTHON_ENV", "ENVIRONMENT", "ENV",
            "DEBUG", "VERBOSE",
            
            # Paths
            "PATH", "PYTHONPATH", "NODE_PATH",
            "VIRTUAL_ENV", "CONDA_DEFAULT_ENV",
            
            # Version managers
            "NVM_DIR", "PYENV_ROOT", "RBENV_ROOT",
            
            # CI/CD
            "CI", "GITHUB_ACTIONS", "GITLAB_CI", "JENKINS_URL",
            "BUILD_NUMBER", "BUILD_ID",
            
            # Cloud/Docker
            "DOCKER_HOST", "KUBERNETES_SERVICE_HOST",
            "AWS_REGION", "AZURE_LOCATION", "GCP_PROJECT",
            
            # Common application vars
            "PORT", "HOST", "DATABASE_URL", "REDIS_URL"
        ]
        
        env_vars = {}
        for var in relevant_vars:
            value = os.getenv(var)
            if value:
                # Sanitize sensitive information
                if any(sensitive in var.lower() for sensitive in ["key", "secret", "token", "password"]):
                    env_vars[var] = "***REDACTED***"
                else:
                    env_vars[var] = value
        
        return env_vars
    
    def get_project_summary(self) -> str:
        """Get a human-readable project summary."""
        context = self.get_context()
        
        summary_parts = [
            f"Working Directory: {context.working_directory}"
        ]
        
        if context.project_type:
            summary_parts.append(f"Project Type: {context.project_type}")
        
        if context.git_repository:
            summary_parts.append(f"Git Repository: {context.git_repository}")
            if context.git_branch:
                summary_parts.append(f"Current Branch: {context.git_branch}")
        
        if context.python_version:
            summary_parts.append(f"Python Version: {context.python_version}")
        
        if context.node_version:
            summary_parts.append(f"Node.js Version: {context.node_version}")
        
        if context.package_files:
            summary_parts.append(f"Package Files: {', '.join(context.package_files)}")
        
        return "\n".join(summary_parts)
    
    def invalidate_cache(self) -> None:
        """Invalidate the context cache."""
        self._cache = None
        self._cache_timestamp = None
