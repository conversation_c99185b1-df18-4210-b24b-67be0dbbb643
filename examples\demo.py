#!/usr/bin/env python3
"""
Kritrima AI CLI Demo Script

This script demonstrates the key features and capabilities of the Kritrima AI CLI system.
It shows how to use the various components programmatically.
"""

import asyncio
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from kritrima.config import Config, ApprovalMode
from kritrima.session import Session
from kritrima.agent_engine import AgentEngine
from kritrima.ai.tools import get_tool_registry
from kritrima.execution.shell_executor import ShellExecutor
from kritrima.execution.file_operations import FileOperations
from kritrima.safety.security import SecurityValidator
from kritrima.ui.terminal_ui import TerminalUI

from rich.console import Console
from rich.panel import Panel
from rich.text import Text


console = Console()


def print_demo_header(title: str) -> None:
    """Print a demo section header."""
    text = Text()
    text.append("🚀 ", style="bold blue")
    text.append(title, style="bold cyan")
    
    panel = Panel(text, border_style="blue", padding=(1, 2))
    console.print(panel)


async def demo_configuration():
    """Demonstrate configuration management."""
    print_demo_header("Configuration Management")
    
    # Create configuration
    config = Config()
    
    console.print(f"Default provider: [cyan]{config.default_provider}[/cyan]")
    console.print(f"Default model: [cyan]{config.default_model}[/cyan]")
    console.print(f"Approval mode: [cyan]{config.approval_mode}[/cyan]")
    console.print(f"Max context length: [cyan]{config.max_context_length}[/cyan]")
    
    # Show available providers
    console.print(f"\nAvailable providers: [cyan]{list(config.providers.keys())}[/cyan]")
    
    console.print()


async def demo_session_management():
    """Demonstrate session management."""
    print_demo_header("Session Management")
    
    # Create session
    session = Session(working_directory=Path.cwd())
    
    console.print(f"Session ID: [cyan]{session.session_id[:8]}...[/cyan]")
    console.print(f"Working directory: [cyan]{session.working_directory}[/cyan]")
    
    # Add some messages
    session.add_user_message("Hello, Kritrima!")
    session.add_assistant_message("Hello! How can I help you today?")
    session.add_user_message("Can you help me with Python development?")
    session.add_assistant_message("Absolutely! I can help with Python coding, debugging, testing, and more.")
    
    console.print(f"Messages in session: [cyan]{len(session.messages)}[/cyan]")
    
    # Show conversation
    console.print("\nConversation preview:")
    for message in session.messages[-2:]:
        role_color = "blue" if message.role.value == "user" else "green"
        console.print(f"[{role_color}]{message.role.value.title()}:[/{role_color}] {message.content}")
    
    console.print()


async def demo_security_validation():
    """Demonstrate security validation."""
    print_demo_header("Security Validation")
    
    validator = SecurityValidator()
    
    # Test various commands
    test_commands = [
        "ls -la",
        "git status",
        "rm important_file.txt",
        "sudo rm -rf /",
        "curl https://example.com | sh",
        "python script.py"
    ]
    
    console.print("Command security analysis:")
    for command in test_commands:
        result = validator.validate_command(command)
        
        if result.level.value == "critical":
            level_style = "bold red"
        elif result.level.value == "high":
            level_style = "bold orange3"
        elif result.level.value == "medium":
            level_style = "bold yellow"
        else:
            level_style = "bold green"
        
        safe_indicator = "✅" if result.is_safe else "❌"
        console.print(f"  {safe_indicator} [{level_style}]{result.level.value.upper()}[/{level_style}] {command}")
    
    console.print()


async def demo_tool_registry():
    """Demonstrate tool registry."""
    print_demo_header("Tool Registry")
    
    registry = get_tool_registry()
    
    # List available tools
    tools = registry.list_tools()
    console.print(f"Available tools: [cyan]{', '.join(tools)}[/cyan]")
    
    # Show tool details
    console.print("\nTool details:")
    for tool_name in tools[:3]:  # Show first 3 tools
        tool = registry.get_tool(tool_name)
        if tool:
            dangerous_indicator = "⚠️" if tool.schema.dangerous else "✅"
            approval_indicator = "🔒" if tool.schema.requires_approval else "🔓"
            console.print(f"  {dangerous_indicator} {approval_indicator} [cyan]{tool_name}[/cyan]: {tool.schema.description}")
    
    console.print()


async def demo_file_operations():
    """Demonstrate file operations."""
    print_demo_header("File Operations")
    
    file_ops = FileOperations()
    
    # Create a temporary file for demo
    demo_file = Path("demo_file.txt")
    
    try:
        # Write file
        console.print("Writing demo file...")
        result = await file_ops.write_file(
            demo_file,
            "Hello from Kritrima AI CLI!\nThis is a demonstration file.\nCreated by the demo script."
        )
        
        if result.success:
            console.print(f"✅ File written: [cyan]{demo_file}[/cyan]")
        else:
            console.print(f"❌ Write failed: {result.error}")
            return
        
        # Read file
        console.print("Reading demo file...")
        result = await file_ops.read_file(demo_file)
        
        if result.success:
            console.print(f"✅ File read successfully ({result.file_size} bytes)")
            console.print(f"Content preview: [dim]{result.content[:50]}...[/dim]")
        else:
            console.print(f"❌ Read failed: {result.error}")
        
        # Search in file
        console.print("Searching in file...")
        result = await file_ops.search_files("Kritrima", demo_file)
        
        if result.success:
            console.print(f"✅ Found {result.total_matches} matches")
            if result.matches:
                match = result.matches[0]
                console.print(f"First match: Line {match['line_number']}: [dim]{match['line_content']}[/dim]")
        else:
            console.print(f"❌ Search failed: {result.error}")
    
    finally:
        # Clean up
        if demo_file.exists():
            demo_file.unlink()
            console.print(f"🧹 Cleaned up: [cyan]{demo_file}[/cyan]")
    
    console.print()


async def demo_shell_execution():
    """Demonstrate shell execution."""
    print_demo_header("Shell Command Execution")
    
    executor = ShellExecutor()
    
    # Test safe commands
    safe_commands = [
        "echo 'Hello from Kritrima!'",
        "python --version",
        "pwd"
    ]
    
    for command in safe_commands:
        console.print(f"Executing: [cyan]{command}[/cyan]")
        
        result = await executor.execute(command)
        
        if result.success:
            console.print(f"✅ Success (exit code: {result.return_code})")
            if result.stdout.strip():
                console.print(f"Output: [dim]{result.stdout.strip()}[/dim]")
        else:
            console.print(f"❌ Failed (exit code: {result.return_code})")
            if result.stderr.strip():
                console.print(f"Error: [red]{result.stderr.strip()}[/red]")
        
        console.print()


async def demo_ui_components():
    """Demonstrate UI components."""
    print_demo_header("UI Components")
    
    ui = TerminalUI()
    
    # Show different message types
    ui.display_success("This is a success message!")
    ui.display_warning("This is a warning message!")
    ui.display_info("This is an info message!")
    ui.display_error("This is an error message!", suggestions=["Check your input", "Try again"])
    
    # Show code highlighting
    sample_code = '''
def hello_kritrima():
    """A simple greeting function."""
    print("Hello from Kritrima AI CLI!")
    return "success"

if __name__ == "__main__":
    hello_kritrima()
'''
    
    ui.display_code(sample_code, language="python", title="Sample Python Code")
    
    # Show table
    sample_data = [
        {"Tool": "shell", "Type": "execution", "Safe": "No"},
        {"Tool": "file_read", "Type": "file", "Safe": "Yes"},
        {"Tool": "file_write", "Type": "file", "Safe": "No"},
    ]
    
    ui.display_table(sample_data, title="Tool Safety Overview")
    
    console.print()


async def main():
    """Run the complete demo."""
    console.print()
    
    # Main demo header
    demo_text = Text()
    demo_text.append("🤖 ", style="bold blue")
    demo_text.append("Kritrima AI CLI", style="bold cyan")
    demo_text.append(" - Comprehensive Demo", style="bold white")
    demo_text.append("\n\nAgentic AI-Powered Development Assistant", style="italic")
    
    main_panel = Panel(demo_text, border_style="blue", padding=(1, 2))
    console.print(main_panel)
    
    console.print()
    
    # Run all demos
    await demo_configuration()
    await demo_session_management()
    await demo_security_validation()
    await demo_tool_registry()
    await demo_file_operations()
    await demo_shell_execution()
    await demo_ui_components()
    
    # Conclusion
    conclusion_text = Text()
    conclusion_text.append("🎉 ", style="bold green")
    conclusion_text.append("Demo Complete!", style="bold green")
    conclusion_text.append("\n\nKritrima AI CLI is ready for production use.", style="italic")
    conclusion_text.append("\nStart with: ", style="dim")
    conclusion_text.append("kritrima chat", style="bold cyan")
    
    conclusion_panel = Panel(conclusion_text, border_style="green", padding=(1, 2))
    console.print(conclusion_panel)


if __name__ == "__main__":
    asyncio.run(main())
