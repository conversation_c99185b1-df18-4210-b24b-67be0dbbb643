"""
Comprehensive file operations for the Kritrima AI CLI.

Provides safe file operations including read, write, search, and manipulation
with proper error handling and security checks.
"""

import os
import re
import glob
import shutil
import hashlib
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Iterator, Tuple
from dataclasses import dataclass
from enum import Enum

from ..config import get_config


class FileOperationType(str, Enum):
    """Types of file operations."""
    READ = "read"
    WRITE = "write"
    SEARCH = "search"
    COPY = "copy"
    MOVE = "move"
    DELETE = "delete"
    CREATE_DIR = "create_dir"
    LIST_DIR = "list_dir"


@dataclass
class FileResult:
    """Result of file operation."""
    operation: FileOperationType
    success: bool
    file_path: Optional[str] = None
    content: Optional[str] = None
    error: Optional[str] = None
    file_size: Optional[int] = None
    encoding: Optional[str] = None
    bytes_written: Optional[int] = None
    matches: Optional[List[Dict[str, Any]]] = None
    total_matches: Optional[int] = None
    files_searched: Optional[int] = None
    files_found: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "operation": self.operation.value,
            "success": self.success,
            "file_path": self.file_path,
            "content": self.content,
            "error": self.error,
            "file_size": self.file_size,
            "encoding": self.encoding,
            "bytes_written": self.bytes_written,
            "matches": self.matches,
            "total_matches": self.total_matches,
            "files_searched": self.files_searched,
            "files_found": self.files_found
        }


class FileOperations:
    """
    Comprehensive file operations with safety and error handling.
    
    Provides secure file operations including reading, writing, searching,
    and manipulation with proper validation and error handling.
    """
    
    def __init__(self):
        """Initialize file operations."""
        self.config = get_config()
    
    async def read_file(
        self,
        file_path: Union[str, Path],
        encoding: str = "utf-8",
        max_size: int = 1024 * 1024  # 1MB default
    ) -> FileResult:
        """
        Read file contents safely.
        
        Args:
            file_path: Path to the file
            encoding: File encoding
            max_size: Maximum file size to read in bytes
            
        Returns:
            FileResult with file contents or error
        """
        try:
            file_path = Path(file_path).resolve()
            
            # Check if file exists
            if not file_path.exists():
                return FileResult(
                    operation=FileOperationType.READ,
                    success=False,
                    file_path=str(file_path),
                    error="File does not exist"
                )
            
            # Check if it's a file
            if not file_path.is_file():
                return FileResult(
                    operation=FileOperationType.READ,
                    success=False,
                    file_path=str(file_path),
                    error="Path is not a file"
                )
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size > max_size:
                return FileResult(
                    operation=FileOperationType.READ,
                    success=False,
                    file_path=str(file_path),
                    file_size=file_size,
                    error=f"File too large ({file_size} bytes, max {max_size})"
                )
            
            # Read file content
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            return FileResult(
                operation=FileOperationType.READ,
                success=True,
                file_path=str(file_path),
                content=content,
                file_size=file_size,
                encoding=encoding
            )
        
        except UnicodeDecodeError as e:
            return FileResult(
                operation=FileOperationType.READ,
                success=False,
                file_path=str(file_path),
                error=f"Encoding error: {str(e)}"
            )
        except PermissionError:
            return FileResult(
                operation=FileOperationType.READ,
                success=False,
                file_path=str(file_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.READ,
                success=False,
                file_path=str(file_path),
                error=f"Read failed: {str(e)}"
            )
    
    async def write_file(
        self,
        file_path: Union[str, Path],
        content: str,
        encoding: str = "utf-8",
        mode: str = "write",  # "write" or "append"
        create_directories: bool = True
    ) -> FileResult:
        """
        Write content to file safely.
        
        Args:
            file_path: Path to the file
            content: Content to write
            encoding: File encoding
            mode: Write mode ("write" or "append")
            create_directories: Create parent directories if needed
            
        Returns:
            FileResult with write status
        """
        try:
            file_path = Path(file_path).resolve()
            
            # Create parent directories if needed
            if create_directories:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Check if parent directory exists
            if not file_path.parent.exists():
                return FileResult(
                    operation=FileOperationType.WRITE,
                    success=False,
                    file_path=str(file_path),
                    error="Parent directory does not exist"
                )
            
            # Determine write mode
            write_mode = "a" if mode == "append" else "w"
            
            # Write content
            with open(file_path, write_mode, encoding=encoding) as f:
                bytes_written = f.write(content)
            
            return FileResult(
                operation=FileOperationType.WRITE,
                success=True,
                file_path=str(file_path),
                bytes_written=len(content.encode(encoding)),
                encoding=encoding
            )
        
        except PermissionError:
            return FileResult(
                operation=FileOperationType.WRITE,
                success=False,
                file_path=str(file_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.WRITE,
                success=False,
                file_path=str(file_path),
                error=f"Write failed: {str(e)}"
            )
    
    async def search_files(
        self,
        pattern: str,
        file_path: Union[str, Path],
        recursive: bool = False,
        case_sensitive: bool = True,
        max_results: int = 100
    ) -> FileResult:
        """
        Search for patterns within files.
        
        Args:
            pattern: Search pattern (regex supported)
            file_path: File or directory to search in
            recursive: Search recursively in directories
            case_sensitive: Case sensitive search
            max_results: Maximum number of results
            
        Returns:
            FileResult with search results
        """
        try:
            file_path = Path(file_path).resolve()
            
            if not file_path.exists():
                return FileResult(
                    operation=FileOperationType.SEARCH,
                    success=False,
                    file_path=str(file_path),
                    error="Path does not exist"
                )
            
            # Compile regex pattern
            flags = 0 if case_sensitive else re.IGNORECASE
            try:
                regex = re.compile(pattern, flags)
            except re.error as e:
                return FileResult(
                    operation=FileOperationType.SEARCH,
                    success=False,
                    file_path=str(file_path),
                    error=f"Invalid regex pattern: {str(e)}"
                )
            
            matches = []
            files_searched = 0
            total_matches = 0
            
            # Get files to search
            if file_path.is_file():
                files_to_search = [file_path]
            else:
                # Directory search
                if recursive:
                    files_to_search = list(file_path.rglob("*"))
                else:
                    files_to_search = list(file_path.glob("*"))
                
                # Filter to only files
                files_to_search = [f for f in files_to_search if f.is_file()]
            
            # Search in files
            for search_file in files_to_search:
                if len(matches) >= max_results:
                    break
                
                try:
                    # Skip binary files (basic check)
                    if self._is_binary_file(search_file):
                        continue
                    
                    files_searched += 1
                    
                    with open(search_file, 'r', encoding='utf-8', errors='ignore') as f:
                        for line_num, line in enumerate(f, 1):
                            if len(matches) >= max_results:
                                break
                            
                            match = regex.search(line)
                            if match:
                                total_matches += 1
                                matches.append({
                                    "file": str(search_file),
                                    "line_number": line_num,
                                    "line_content": line.strip(),
                                    "match_start": match.start(),
                                    "match_end": match.end(),
                                    "matched_text": match.group()
                                })
                
                except (UnicodeDecodeError, PermissionError):
                    # Skip files that can't be read
                    continue
            
            return FileResult(
                operation=FileOperationType.SEARCH,
                success=True,
                file_path=str(file_path),
                matches=matches,
                total_matches=total_matches,
                files_searched=files_searched
            )
        
        except Exception as e:
            return FileResult(
                operation=FileOperationType.SEARCH,
                success=False,
                file_path=str(file_path),
                error=f"Search failed: {str(e)}"
            )
    
    def _is_binary_file(self, file_path: Path) -> bool:
        """Check if file is binary by reading first few bytes."""
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                return b'\0' in chunk
        except Exception:
            return True  # Assume binary if can't read

    async def copy_file(
        self,
        source_path: Union[str, Path],
        destination_path: Union[str, Path],
        overwrite: bool = False
    ) -> FileResult:
        """
        Copy a file to another location.

        Args:
            source_path: Source file path
            destination_path: Destination file path
            overwrite: Whether to overwrite existing files

        Returns:
            FileResult with copy status
        """
        try:
            source_path = Path(source_path).resolve()
            destination_path = Path(destination_path).resolve()

            # Check source exists
            if not source_path.exists():
                return FileResult(
                    operation=FileOperationType.COPY,
                    success=False,
                    file_path=str(source_path),
                    error="Source file does not exist"
                )

            # Check if source is a file
            if not source_path.is_file():
                return FileResult(
                    operation=FileOperationType.COPY,
                    success=False,
                    file_path=str(source_path),
                    error="Source is not a file"
                )

            # Check if destination exists and overwrite policy
            if destination_path.exists() and not overwrite:
                return FileResult(
                    operation=FileOperationType.COPY,
                    success=False,
                    file_path=str(destination_path),
                    error="Destination exists and overwrite is disabled"
                )

            # Create destination directory if needed
            destination_path.parent.mkdir(parents=True, exist_ok=True)

            # Copy file
            shutil.copy2(source_path, destination_path)

            return FileResult(
                operation=FileOperationType.COPY,
                success=True,
                file_path=str(destination_path),
                file_size=destination_path.stat().st_size
            )

        except PermissionError:
            return FileResult(
                operation=FileOperationType.COPY,
                success=False,
                file_path=str(source_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.COPY,
                success=False,
                file_path=str(source_path),
                error=f"Copy failed: {str(e)}"
            )

    async def move_file(
        self,
        source_path: Union[str, Path],
        destination_path: Union[str, Path],
        overwrite: bool = False
    ) -> FileResult:
        """
        Move a file to another location.

        Args:
            source_path: Source file path
            destination_path: Destination file path
            overwrite: Whether to overwrite existing files

        Returns:
            FileResult with move status
        """
        try:
            source_path = Path(source_path).resolve()
            destination_path = Path(destination_path).resolve()

            # Check source exists
            if not source_path.exists():
                return FileResult(
                    operation=FileOperationType.MOVE,
                    success=False,
                    file_path=str(source_path),
                    error="Source file does not exist"
                )

            # Check if destination exists and overwrite policy
            if destination_path.exists() and not overwrite:
                return FileResult(
                    operation=FileOperationType.MOVE,
                    success=False,
                    file_path=str(destination_path),
                    error="Destination exists and overwrite is disabled"
                )

            # Create destination directory if needed
            destination_path.parent.mkdir(parents=True, exist_ok=True)

            # Move file
            shutil.move(str(source_path), str(destination_path))

            return FileResult(
                operation=FileOperationType.MOVE,
                success=True,
                file_path=str(destination_path)
            )

        except PermissionError:
            return FileResult(
                operation=FileOperationType.MOVE,
                success=False,
                file_path=str(source_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.MOVE,
                success=False,
                file_path=str(source_path),
                error=f"Move failed: {str(e)}"
            )

    async def delete_file(
        self,
        file_path: Union[str, Path],
        force: bool = False
    ) -> FileResult:
        """
        Delete a file.

        Args:
            file_path: Path to the file to delete
            force: Force deletion even if file is read-only

        Returns:
            FileResult with deletion status
        """
        try:
            file_path = Path(file_path).resolve()

            # Check if file exists
            if not file_path.exists():
                return FileResult(
                    operation=FileOperationType.DELETE,
                    success=False,
                    file_path=str(file_path),
                    error="File does not exist"
                )

            # Check if it's a file
            if not file_path.is_file():
                return FileResult(
                    operation=FileOperationType.DELETE,
                    success=False,
                    file_path=str(file_path),
                    error="Path is not a file"
                )

            # Handle read-only files
            if force and not os.access(file_path, os.W_OK):
                file_path.chmod(0o666)

            # Delete file
            file_path.unlink()

            return FileResult(
                operation=FileOperationType.DELETE,
                success=True,
                file_path=str(file_path)
            )

        except PermissionError:
            return FileResult(
                operation=FileOperationType.DELETE,
                success=False,
                file_path=str(file_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.DELETE,
                success=False,
                file_path=str(file_path),
                error=f"Delete failed: {str(e)}"
            )

    async def list_directory(
        self,
        directory_path: Union[str, Path],
        recursive: bool = False,
        include_hidden: bool = False,
        pattern: Optional[str] = None
    ) -> FileResult:
        """
        List directory contents.

        Args:
            directory_path: Path to the directory
            recursive: List recursively
            include_hidden: Include hidden files
            pattern: Glob pattern to filter files

        Returns:
            FileResult with directory listing
        """
        try:
            directory_path = Path(directory_path).resolve()

            # Check if directory exists
            if not directory_path.exists():
                return FileResult(
                    operation=FileOperationType.LIST_DIR,
                    success=False,
                    file_path=str(directory_path),
                    error="Directory does not exist"
                )

            # Check if it's a directory
            if not directory_path.is_dir():
                return FileResult(
                    operation=FileOperationType.LIST_DIR,
                    success=False,
                    file_path=str(directory_path),
                    error="Path is not a directory"
                )

            files_found = []

            # Get files
            if recursive:
                if pattern:
                    paths = directory_path.rglob(pattern)
                else:
                    paths = directory_path.rglob("*")
            else:
                if pattern:
                    paths = directory_path.glob(pattern)
                else:
                    paths = directory_path.glob("*")

            for path in paths:
                # Skip hidden files if not requested
                if not include_hidden and path.name.startswith('.'):
                    continue

                files_found.append(str(path))

            return FileResult(
                operation=FileOperationType.LIST_DIR,
                success=True,
                file_path=str(directory_path),
                files_found=sorted(files_found)
            )

        except PermissionError:
            return FileResult(
                operation=FileOperationType.LIST_DIR,
                success=False,
                file_path=str(directory_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.LIST_DIR,
                success=False,
                file_path=str(directory_path),
                error=f"List failed: {str(e)}"
            )

    async def create_directory(
        self,
        directory_path: Union[str, Path],
        parents: bool = True,
        exist_ok: bool = True
    ) -> FileResult:
        """
        Create a directory.

        Args:
            directory_path: Path to the directory to create
            parents: Create parent directories if needed
            exist_ok: Don't raise error if directory exists

        Returns:
            FileResult with creation status
        """
        try:
            directory_path = Path(directory_path).resolve()

            # Create directory
            directory_path.mkdir(parents=parents, exist_ok=exist_ok)

            return FileResult(
                operation=FileOperationType.CREATE_DIR,
                success=True,
                file_path=str(directory_path)
            )

        except FileExistsError:
            return FileResult(
                operation=FileOperationType.CREATE_DIR,
                success=False,
                file_path=str(directory_path),
                error="Directory already exists"
            )
        except PermissionError:
            return FileResult(
                operation=FileOperationType.CREATE_DIR,
                success=False,
                file_path=str(directory_path),
                error="Permission denied"
            )
        except Exception as e:
            return FileResult(
                operation=FileOperationType.CREATE_DIR,
                success=False,
                file_path=str(directory_path),
                error=f"Create failed: {str(e)}"
            )
