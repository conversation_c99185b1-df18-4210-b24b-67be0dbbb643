# Shell Commands Reference for Kritrima AI CLI

This document provides a comprehensive reference for shell commands that can be executed through the Kritrima AI CLI system. The AI agent has access to all these commands and can execute them safely with proper approval workflows.

## File Operations

### Basic File Commands
- `ls` / `dir` - List directory contents
- `pwd` - Print working directory
- `cd <directory>` - Change directory
- `mkdir <directory>` - Create directory
- `rmdir <directory>` - Remove empty directory
- `rm <file>` - Remove file
- `cp <source> <dest>` - Copy file
- `mv <source> <dest>` - Move/rename file
- `touch <file>` - Create empty file or update timestamp

### File Content Operations
- `cat <file>` - Display file contents
- `head <file>` - Display first lines of file
- `tail <file>` - Display last lines of file
- `less <file>` / `more <file>` - View file with pagination
- `grep <pattern> <file>` - Search for pattern in file
- `find <path> -name <pattern>` - Find files by name
- `locate <pattern>` - Find files using database
- `which <command>` - Find command location
- `file <file>` - Determine file type

### Text Processing
- `wc <file>` - Count lines, words, characters
- `sort <file>` - Sort lines in file
- `uniq <file>` - Remove duplicate lines
- `cut -d<delimiter> -f<fields> <file>` - Extract columns
- `awk '<pattern> {<action>}' <file>` - Text processing
- `sed 's/<pattern>/<replacement>/g' <file>` - Stream editor
- `tr '<set1>' '<set2>'` - Translate characters

## System Information

### Process Management
- `ps aux` - List all processes
- `top` / `htop` - Display running processes
- `kill <pid>` - Terminate process by ID
- `killall <name>` - Terminate processes by name
- `jobs` - List active jobs
- `bg` / `fg` - Background/foreground jobs
- `nohup <command>` - Run command immune to hangups

### System Status
- `uptime` - System uptime and load
- `whoami` - Current username
- `id` - User and group IDs
- `groups` - User groups
- `date` - Current date and time
- `cal` - Calendar
- `df -h` - Disk space usage
- `du -h <directory>` - Directory size
- `free -h` - Memory usage
- `lscpu` - CPU information
- `lsblk` - Block devices

### Network Commands
- `ping <host>` - Test network connectivity
- `curl <url>` - Transfer data from servers
- `wget <url>` - Download files
- `netstat -tuln` - Network connections
- `ss -tuln` - Socket statistics
- `ip addr` - Network interfaces
- `dig <domain>` - DNS lookup
- `nslookup <domain>` - DNS lookup

## Development Tools

### Version Control (Git)
- `git status` - Repository status
- `git log` - Commit history
- `git diff` - Show changes
- `git add <file>` - Stage changes
- `git commit -m "<message>"` - Commit changes
- `git push` - Push to remote
- `git pull` - Pull from remote
- `git branch` - List branches
- `git checkout <branch>` - Switch branch
- `git merge <branch>` - Merge branch
- `git clone <url>` - Clone repository

### Python Development
- `python --version` - Python version
- `pip list` - Installed packages
- `pip install <package>` - Install package
- `pip uninstall <package>` - Uninstall package
- `pip freeze` - List installed packages with versions
- `python -m venv <name>` - Create virtual environment
- `source <venv>/bin/activate` - Activate virtual environment
- `deactivate` - Deactivate virtual environment
- `python <script.py>` - Run Python script
- `python -m <module>` - Run Python module

### Node.js Development
- `node --version` - Node.js version
- `npm --version` - npm version
- `npm list` - Installed packages
- `npm install <package>` - Install package
- `npm uninstall <package>` - Uninstall package
- `npm run <script>` - Run npm script
- `npm start` - Start application
- `npm test` - Run tests
- `npm build` - Build application
- `yarn install` - Install with Yarn
- `yarn add <package>` - Add package with Yarn

### Docker Commands
- `docker --version` - Docker version
- `docker ps` - Running containers
- `docker ps -a` - All containers
- `docker images` - List images
- `docker build -t <name> .` - Build image
- `docker run <image>` - Run container
- `docker stop <container>` - Stop container
- `docker rm <container>` - Remove container
- `docker rmi <image>` - Remove image
- `docker logs <container>` - Container logs
- `docker exec -it <container> bash` - Execute in container

### Database Commands
- `mysql -u <user> -p` - Connect to MySQL
- `psql -U <user> -d <database>` - Connect to PostgreSQL
- `sqlite3 <database>` - Connect to SQLite
- `mongosh` - Connect to MongoDB
- `redis-cli` - Connect to Redis

## Archive and Compression

### Archive Operations
- `tar -czf <archive.tar.gz> <files>` - Create compressed archive
- `tar -xzf <archive.tar.gz>` - Extract compressed archive
- `tar -tzf <archive.tar.gz>` - List archive contents
- `zip -r <archive.zip> <directory>` - Create ZIP archive
- `unzip <archive.zip>` - Extract ZIP archive
- `gzip <file>` - Compress file
- `gunzip <file.gz>` - Decompress file

## Environment and Configuration

### Environment Variables
- `env` - List environment variables
- `export VAR=value` - Set environment variable
- `echo $VAR` - Display variable value
- `unset VAR` - Remove variable
- `printenv` - Print environment

### Path and Permissions
- `chmod <permissions> <file>` - Change file permissions
- `chown <user>:<group> <file>` - Change file ownership
- `umask` - Default permissions mask
- `ln -s <target> <link>` - Create symbolic link
- `readlink <link>` - Read symbolic link

## Package Management

### Ubuntu/Debian (apt)
- `apt update` - Update package list
- `apt upgrade` - Upgrade packages
- `apt install <package>` - Install package
- `apt remove <package>` - Remove package
- `apt search <pattern>` - Search packages
- `apt list --installed` - List installed packages

### CentOS/RHEL (yum/dnf)
- `yum update` / `dnf update` - Update packages
- `yum install <package>` / `dnf install <package>` - Install package
- `yum remove <package>` / `dnf remove <package>` - Remove package
- `yum search <pattern>` / `dnf search <pattern>` - Search packages

### macOS (Homebrew)
- `brew update` - Update Homebrew
- `brew upgrade` - Upgrade packages
- `brew install <package>` - Install package
- `brew uninstall <package>` - Uninstall package
- `brew search <pattern>` - Search packages
- `brew list` - List installed packages

## Security Considerations

### Safe Commands (Always Allowed)
These commands are considered safe and can be executed without approval:
- File viewing: `cat`, `head`, `tail`, `less`, `more`
- Directory listing: `ls`, `dir`, `pwd`
- Information gathering: `ps`, `whoami`, `date`, `uptime`
- Version checking: `python --version`, `node --version`
- Git status: `git status`, `git log`, `git diff`

### Commands Requiring Approval
These commands require user approval before execution:
- File modification: `rm`, `mv`, `cp`, `chmod`, `chown`
- System changes: `sudo`, `su`, package installation
- Network operations: `curl`, `wget`, `ssh`, `ftp`
- Process control: `kill`, `killall`

### Dangerous Commands (Blocked)
These commands are blocked for security reasons:
- System destruction: `rm -rf /`, `format`, `fdisk`
- System control: `shutdown`, `reboot`, `halt`
- Privilege escalation: `sudo rm`, `sudo format`
- Network attacks: `nmap -sS`, `hping3 --flood`

## Best Practices

1. **Always review commands** before execution, especially those involving file modification
2. **Use version control** for important files before making changes
3. **Test in safe environments** before running commands on production systems
4. **Understand command implications** - read documentation when unsure
5. **Keep backups** of important data
6. **Use least privilege** - don't run as root unless necessary
7. **Monitor system logs** for unusual activity

## Getting Help

- `man <command>` - Manual page for command
- `<command> --help` - Command help
- `info <command>` - Info documentation
- `which <command>` - Find command location
- `type <command>` - Command type information

This reference provides the foundation for safe and effective command execution through the Kritrima AI CLI system. The AI agent uses this knowledge to assist with development tasks while maintaining security and safety standards.
