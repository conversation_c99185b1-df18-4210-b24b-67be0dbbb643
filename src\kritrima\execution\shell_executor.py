"""
Safe shell command execution with monitoring and security checks.

Provides secure command execution with timeout, output capture, and safety validation.
"""

import asyncio
import os
import sys
import time
import shlex
import platform
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
from dataclasses import dataclass
from enum import Enum

import psutil

from ..config import get_config
from ..safety.security import SecurityValidator


class CommandStatus(str, Enum):
    """Command execution status."""
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    SECURITY_BLOCKED = "security_blocked"


@dataclass
class CommandResult:
    """Result of command execution."""
    command: str
    status: CommandStatus
    return_code: int
    stdout: str
    stderr: str
    execution_time: float
    working_directory: Optional[str] = None
    process_id: Optional[int] = None
    error_message: Optional[str] = None
    
    @property
    def success(self) -> bool:
        """Check if command executed successfully."""
        return self.status == CommandStatus.SUCCESS and self.return_code == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "command": self.command,
            "status": self.status.value,
            "return_code": self.return_code,
            "stdout": self.stdout,
            "stderr": self.stderr,
            "execution_time": self.execution_time,
            "working_directory": self.working_directory,
            "process_id": self.process_id,
            "error_message": self.error_message,
            "success": self.success
        }


class ShellExecutor:
    """
    Secure shell command executor with monitoring and safety features.
    
    Provides safe execution of shell commands with timeout, output capture,
    security validation, and process monitoring.
    """
    
    def __init__(self):
        """Initialize shell executor."""
        self.config = get_config()
        self.security_validator = SecurityValidator()
        self.active_processes: Dict[int, asyncio.subprocess.Process] = {}
    
    async def execute(
        self,
        command: str,
        working_directory: Optional[Union[str, Path]] = None,
        timeout: Optional[int] = None,
        capture_output: bool = True,
        shell: bool = True,
        env: Optional[Dict[str, str]] = None,
        check_security: bool = True
    ) -> CommandResult:
        """
        Execute a shell command safely.
        
        Args:
            command: Command to execute
            working_directory: Working directory for execution
            timeout: Timeout in seconds (defaults to config value)
            capture_output: Whether to capture stdout/stderr
            shell: Whether to use shell for execution
            env: Environment variables
            check_security: Whether to perform security checks
            
        Returns:
            CommandResult with execution details
        """
        start_time = time.time()
        timeout = timeout or self.config.command_timeout
        
        # Security validation
        if check_security:
            security_result = self.security_validator.validate_command(command)
            if not security_result.is_safe:
                return CommandResult(
                    command=command,
                    status=CommandStatus.SECURITY_BLOCKED,
                    return_code=-1,
                    stdout="",
                    stderr="",
                    execution_time=0.0,
                    working_directory=str(working_directory) if working_directory else None,
                    error_message=f"Security check failed: {security_result.reason}"
                )
        
        # Prepare working directory
        if working_directory:
            working_directory = Path(working_directory).resolve()
            if not working_directory.exists():
                return CommandResult(
                    command=command,
                    status=CommandStatus.FAILED,
                    return_code=-1,
                    stdout="",
                    stderr="",
                    execution_time=0.0,
                    working_directory=str(working_directory),
                    error_message=f"Working directory does not exist: {working_directory}"
                )
        
        # Prepare environment
        exec_env = os.environ.copy()
        if env:
            exec_env.update(env)
        
        try:
            # Create subprocess
            if shell and platform.system() != "Windows":
                # Use shell on Unix-like systems
                process = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE if capture_output else None,
                    stderr=asyncio.subprocess.PIPE if capture_output else None,
                    cwd=working_directory,
                    env=exec_env
                )
            else:
                # Use exec on Windows or when shell=False
                if platform.system() == "Windows":
                    # Use PowerShell on Windows
                    cmd_args = ["powershell", "-Command", command]
                else:
                    # Parse command for exec
                    cmd_args = shlex.split(command)
                
                process = await asyncio.create_subprocess_exec(
                    *cmd_args,
                    stdout=asyncio.subprocess.PIPE if capture_output else None,
                    stderr=asyncio.subprocess.PIPE if capture_output else None,
                    cwd=working_directory,
                    env=exec_env
                )
            
            # Track active process
            if process.pid:
                self.active_processes[process.pid] = process
            
            try:
                # Wait for completion with timeout
                stdout_data, stderr_data = await asyncio.wait_for(
                    process.communicate(),
                    timeout=timeout
                )
                
                execution_time = time.time() - start_time
                
                # Decode output
                stdout = stdout_data.decode('utf-8', errors='replace') if stdout_data else ""
                stderr = stderr_data.decode('utf-8', errors='replace') if stderr_data else ""
                
                # Determine status
                if process.returncode == 0:
                    status = CommandStatus.SUCCESS
                else:
                    status = CommandStatus.FAILED
                
                return CommandResult(
                    command=command,
                    status=status,
                    return_code=process.returncode or 0,
                    stdout=stdout,
                    stderr=stderr,
                    execution_time=execution_time,
                    working_directory=str(working_directory) if working_directory else None,
                    process_id=process.pid
                )
            
            except asyncio.TimeoutError:
                # Handle timeout
                await self._terminate_process(process)
                execution_time = time.time() - start_time
                
                return CommandResult(
                    command=command,
                    status=CommandStatus.TIMEOUT,
                    return_code=-1,
                    stdout="",
                    stderr="",
                    execution_time=execution_time,
                    working_directory=str(working_directory) if working_directory else None,
                    process_id=process.pid,
                    error_message=f"Command timed out after {timeout} seconds"
                )
            
            finally:
                # Clean up process tracking
                if process.pid and process.pid in self.active_processes:
                    del self.active_processes[process.pid]
        
        except Exception as e:
            execution_time = time.time() - start_time
            
            return CommandResult(
                command=command,
                status=CommandStatus.FAILED,
                return_code=-1,
                stdout="",
                stderr="",
                execution_time=execution_time,
                working_directory=str(working_directory) if working_directory else None,
                error_message=f"Execution failed: {str(e)}"
            )
    
    async def _terminate_process(self, process: asyncio.subprocess.Process) -> None:
        """Terminate a process gracefully."""
        try:
            # Try graceful termination first
            process.terminate()
            
            # Wait a bit for graceful shutdown
            try:
                await asyncio.wait_for(process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                # Force kill if graceful termination fails
                process.kill()
                await process.wait()
        
        except Exception:
            # Process might already be dead
            pass
    
    async def execute_multiple(
        self,
        commands: List[str],
        working_directory: Optional[Union[str, Path]] = None,
        timeout: Optional[int] = None,
        parallel: bool = False,
        stop_on_error: bool = True
    ) -> List[CommandResult]:
        """
        Execute multiple commands.
        
        Args:
            commands: List of commands to execute
            working_directory: Working directory for execution
            timeout: Timeout per command in seconds
            parallel: Whether to execute commands in parallel
            stop_on_error: Whether to stop on first error (sequential only)
            
        Returns:
            List of CommandResult objects
        """
        if parallel:
            # Execute commands in parallel
            tasks = [
                self.execute(cmd, working_directory, timeout)
                for cmd in commands
            ]
            return await asyncio.gather(*tasks, return_exceptions=False)
        else:
            # Execute commands sequentially
            results = []
            for command in commands:
                result = await self.execute(command, working_directory, timeout)
                results.append(result)
                
                if stop_on_error and not result.success:
                    break
            
            return results
    
    def get_active_processes(self) -> List[int]:
        """Get list of active process IDs."""
        return list(self.active_processes.keys())
    
    async def terminate_process(self, process_id: int) -> bool:
        """
        Terminate a specific process.
        
        Args:
            process_id: Process ID to terminate
            
        Returns:
            True if process was terminated, False if not found
        """
        process = self.active_processes.get(process_id)
        if process:
            await self._terminate_process(process)
            return True
        return False
    
    async def terminate_all_processes(self) -> int:
        """
        Terminate all active processes.
        
        Returns:
            Number of processes terminated
        """
        processes = list(self.active_processes.values())
        
        for process in processes:
            await self._terminate_process(process)
        
        self.active_processes.clear()
        return len(processes)
    
    def is_command_safe(self, command: str) -> Tuple[bool, str]:
        """
        Check if a command is safe to execute.
        
        Args:
            command: Command to check
            
        Returns:
            Tuple of (is_safe, reason)
        """
        result = self.security_validator.validate_command(command)
        return result.is_safe, result.reason
