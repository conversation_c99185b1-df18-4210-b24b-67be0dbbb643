# Kritrima AI CLI Configuration
# This is the default configuration file for Kritrima AI CLI

# Core Settings
debug: false
verbose: false

# AI Provider Settings
default_provider: "openai"
default_model: "gpt-4"
max_context_length: 8000

# Approval and Safety Settings
approval_mode: "suggest"  # suggest, auto-edit, full-auto

# Safe commands that don't require approval
safe_commands:
  - "ls"
  - "dir" 
  - "pwd"
  - "echo"
  - "cat"
  - "head"
  - "tail"
  - "grep"
  - "find"
  - "git status"
  - "git log"
  - "git diff"
  - "npm list"
  - "pip list"
  - "python --version"
  - "node --version"

# Commands that are considered dangerous and require extra caution
dangerous_commands:
  - "rm"
  - "del"
  - "format"
  - "fdisk"
  - "dd"
  - "mkfs"
  - "shutdown"
  - "reboot"

# Storage Settings
session_storage_path: "~/.kritrima/sessions"
cache_path: "~/.kritrima/cache"
log_path: "~/.kritrima/logs"

# UI Settings
use_rich_ui: true
show_progress: true
color_output: true

# Performance Settings
max_concurrent_operations: 5
command_timeout: 300  # 5 minutes
retry_attempts: 3

# Provider Configurations
providers:
  openai:
    base_url: "https://api.openai.com/v1"
    models:
      - "gpt-4"
      - "gpt-4-turbo"
      - "gpt-3.5-turbo"
    default_model: "gpt-4"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30

  deepseek:
    base_url: "https://api.deepseek.com/v1"
    models:
      - "deepseek-chat"
      - "deepseek-coder"
    default_model: "deepseek-chat"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30

  ollama:
    base_url: "http://localhost:11434/v1"
    models:
      - "llama2"
      - "codellama"
      - "mistral"
    default_model: "llama2"
    max_tokens: 4096
    temperature: 0.7
    timeout: 60

  azure:
    base_url: "${AZURE_OPENAI_ENDPOINT}/openai/deployments"
    models:
      - "gpt-4"
      - "gpt-35-turbo"
    default_model: "gpt-4"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30

# Tool Settings
tools:
  shell:
    enabled: true
    requires_approval: true
    dangerous: true
    timeout: 300

  file_read:
    enabled: true
    requires_approval: false
    dangerous: false
    max_file_size: 1048576  # 1MB

  file_write:
    enabled: true
    requires_approval: true
    dangerous: true

  file_search:
    enabled: true
    requires_approval: false
    dangerous: false
    max_results: 100

# Security Settings
security:
  enable_command_validation: true
  block_critical_commands: true
  require_approval_for_system_changes: true
  log_all_commands: true

# Logging Settings
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# Context Settings
context:
  auto_discover_project: true
  include_git_info: true
  include_environment_vars: true
  cache_ttl: 300  # 5 minutes
  max_recent_files: 20

# Session Settings
session:
  auto_save: true
  max_message_history: 1000
  cleanup_old_sessions: true
  max_session_age_days: 30

# Network Settings
network:
  timeout: 30
  max_retries: 3
  retry_delay: 1.0
  exponential_backoff: true

# Development Settings (for development mode)
development:
  enable_debug_tools: false
  mock_ai_responses: false
  test_mode: false
  verbose_logging: false
