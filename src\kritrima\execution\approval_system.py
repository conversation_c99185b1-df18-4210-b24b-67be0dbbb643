"""
Approval system for safe command and tool execution.

Provides different approval modes and interactive approval workflows.
"""

import json
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.table import Table
from rich.prompt import Confirm

from ..config import get_config, ApprovalMode
from ..ai.tools import ToolSchema
from ..safety.security import SecurityValidator, SecurityLevel


@dataclass
class ApprovalRequest:
    """Request for approval of an operation."""
    tool_name: str
    parameters: Dict[str, Any]
    tool_schema: ToolSchema
    security_level: SecurityLevel
    security_reason: str
    auto_approved: bool = False
    approved: bool = False
    reason: str = ""


class ApprovalSystem:
    """
    Manages approval workflows for tool execution and command running.
    
    Supports different approval modes from fully manual to fully automatic
    with security-based decision making.
    """
    
    def __init__(self):
        """Initialize approval system."""
        self.config = get_config()
        self.console = Console()
        self.security_validator = SecurityValidator()
        
        # Track approval history for learning
        self.approval_history: List[ApprovalRequest] = []
    
    async def request_approval(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        tool_schema: ToolSchema,
        custom_message: Optional[str] = None
    ) -> bool:
        """
        Request approval for tool execution.
        
        Args:
            tool_name: Name of the tool to execute
            parameters: Tool parameters
            tool_schema: Tool schema definition
            custom_message: Custom approval message
            
        Returns:
            True if approved, False otherwise
        """
        # Create approval request
        request = ApprovalRequest(
            tool_name=tool_name,
            parameters=parameters,
            tool_schema=tool_schema,
            security_level=SecurityLevel.LOW,
            security_reason="No security analysis performed"
        )
        
        # Perform security analysis
        if tool_name == "shell":
            command = parameters.get("command", "")
            security_result = self.security_validator.validate_command(command)
            request.security_level = security_result.level
            request.security_reason = security_result.reason
            
            # Block critical security risks regardless of mode
            if security_result.level == SecurityLevel.CRITICAL:
                self.console.print(f"[red]❌ Command blocked for security reasons:[/red]")
                self.console.print(f"[red]{security_result.reason}[/red]")
                if security_result.suggestions:
                    self.console.print("\n[yellow]Suggestions:[/yellow]")
                    for suggestion in security_result.suggestions:
                        self.console.print(f"  • {suggestion}")
                return False
        
        # Apply approval mode logic
        approval_mode = self.config.approval_mode
        
        if approval_mode == ApprovalMode.FULL_AUTO:
            # Auto-approve everything except critical security risks
            request.auto_approved = True
            request.approved = True
            request.reason = "Auto-approved (full-auto mode)"
            
        elif approval_mode == ApprovalMode.AUTO_EDIT:
            # Auto-approve file operations, ask for shell commands
            if tool_name in ["file_read", "file_write", "file_search", "file_copy", "file_move"]:
                request.auto_approved = True
                request.approved = True
                request.reason = "Auto-approved (file operation in auto-edit mode)"
            else:
                request.approved = await self._interactive_approval(request, custom_message)
                
        else:  # ApprovalMode.SUGGEST
            # Ask for approval on everything
            request.approved = await self._interactive_approval(request, custom_message)
        
        # Store in history
        self.approval_history.append(request)
        
        return request.approved
    
    async def _interactive_approval(
        self,
        request: ApprovalRequest,
        custom_message: Optional[str] = None
    ) -> bool:
        """
        Show interactive approval dialog.
        
        Args:
            request: Approval request details
            custom_message: Custom message to display
            
        Returns:
            True if approved, False otherwise
        """
        self.console.print()
        
        # Create approval panel
        panel_content = self._create_approval_panel(request, custom_message)
        self.console.print(panel_content)
        
        # Show security warnings if needed
        if request.security_level in [SecurityLevel.HIGH, SecurityLevel.MEDIUM]:
            self._show_security_warning(request)
        
        # Get user decision
        try:
            approved = Confirm.ask(
                "[bold yellow]Do you want to proceed?[/bold yellow]",
                default=False
            )
            
            if approved:
                request.reason = "User approved"
            else:
                request.reason = "User denied"
            
            return approved
            
        except (KeyboardInterrupt, EOFError):
            request.reason = "User cancelled"
            return False
    
    def _create_approval_panel(
        self,
        request: ApprovalRequest,
        custom_message: Optional[str] = None
    ) -> Panel:
        """Create approval panel for display."""
        text = Text()
        
        # Header
        text.append("🔐 ", style="bold yellow")
        text.append("Approval Required", style="bold yellow")
        text.append("\n\n")
        
        # Custom message
        if custom_message:
            text.append(custom_message, style="italic")
            text.append("\n\n")
        
        # Tool information
        text.append("Tool: ", style="bold")
        text.append(f"{request.tool_name}", style="cyan")
        text.append("\n")
        
        text.append("Description: ", style="bold")
        text.append(f"{request.tool_schema.description}", style="dim")
        text.append("\n\n")
        
        # Parameters
        text.append("Parameters:", style="bold")
        text.append("\n")
        
        for key, value in request.parameters.items():
            text.append(f"  {key}: ", style="bold")
            
            # Format value based on type
            if isinstance(value, str) and len(value) > 100:
                text.append(f"{value[:100]}...", style="dim")
            else:
                text.append(f"{value}", style="white")
            text.append("\n")
        
        # Security level indicator
        text.append("\n")
        text.append("Security Level: ", style="bold")
        
        if request.security_level == SecurityLevel.CRITICAL:
            text.append("CRITICAL", style="bold red")
        elif request.security_level == SecurityLevel.HIGH:
            text.append("HIGH", style="bold orange3")
        elif request.security_level == SecurityLevel.MEDIUM:
            text.append("MEDIUM", style="bold yellow")
        else:
            text.append("LOW", style="bold green")
        
        # Determine panel style based on security level
        if request.security_level == SecurityLevel.CRITICAL:
            border_style = "red"
        elif request.security_level == SecurityLevel.HIGH:
            border_style = "orange3"
        elif request.security_level == SecurityLevel.MEDIUM:
            border_style = "yellow"
        else:
            border_style = "blue"
        
        return Panel(text, border_style=border_style, padding=(1, 2))
    
    def _show_security_warning(self, request: ApprovalRequest) -> None:
        """Show security warning for risky operations."""
        warning_text = Text()
        warning_text.append("⚠️  ", style="bold red")
        warning_text.append("Security Warning", style="bold red")
        warning_text.append("\n\n")
        warning_text.append(request.security_reason, style="red")
        
        # Add suggestions from security validator
        if request.tool_name == "shell":
            command = request.parameters.get("command", "")
            security_result = self.security_validator.validate_command(command)
            if security_result.suggestions:
                warning_text.append("\n\nSuggestions:", style="bold yellow")
                for suggestion in security_result.suggestions:
                    warning_text.append(f"\n  • {suggestion}", style="yellow")
        
        self.console.print(Panel(warning_text, border_style="red", padding=(1, 2)))
    
    def get_approval_history(self, limit: Optional[int] = None) -> List[ApprovalRequest]:
        """
        Get approval history.
        
        Args:
            limit: Maximum number of entries to return
            
        Returns:
            List of approval requests
        """
        history = self.approval_history
        if limit:
            history = history[-limit:]
        return history
    
    def get_approval_stats(self) -> Dict[str, Any]:
        """Get approval statistics."""
        total = len(self.approval_history)
        if total == 0:
            return {
                "total_requests": 0,
                "approved": 0,
                "denied": 0,
                "auto_approved": 0,
                "approval_rate": 0.0,
                "auto_approval_rate": 0.0
            }
        
        approved = sum(1 for req in self.approval_history if req.approved)
        auto_approved = sum(1 for req in self.approval_history if req.auto_approved)
        denied = total - approved
        
        return {
            "total_requests": total,
            "approved": approved,
            "denied": denied,
            "auto_approved": auto_approved,
            "approval_rate": approved / total,
            "auto_approval_rate": auto_approved / total
        }
    
    def clear_history(self) -> None:
        """Clear approval history."""
        self.approval_history.clear()
    
    def set_approval_mode(self, mode: ApprovalMode) -> None:
        """
        Set approval mode.
        
        Args:
            mode: New approval mode
        """
        self.config.approval_mode = mode
        self.console.print(f"[green]Approval mode set to: {mode.value}[/green]")
    
    def get_approval_mode(self) -> ApprovalMode:
        """Get current approval mode."""
        return self.config.approval_mode
    
    def is_tool_auto_approved(self, tool_name: str) -> bool:
        """
        Check if a tool is automatically approved in current mode.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            True if auto-approved, False otherwise
        """
        mode = self.config.approval_mode
        
        if mode == ApprovalMode.FULL_AUTO:
            return True
        elif mode == ApprovalMode.AUTO_EDIT:
            return tool_name in ["file_read", "file_write", "file_search", "file_copy", "file_move"]
        else:
            return False
