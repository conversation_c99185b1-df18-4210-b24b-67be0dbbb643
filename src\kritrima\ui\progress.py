"""
Progress management for long-running operations.

Provides progress indicators, spinners, and status updates for various operations
like file processing, command execution, and AI interactions.
"""

import time
import threading
from typing import Optional, Any, Callable
from contextlib import contextmanager
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.console import <PERSON>sole
from rich.live import Live
from rich.spinner import Spinner
from rich.text import Text


class ProgressManager:
    """
    Manager for progress indicators and status updates.
    """
    
    def __init__(self, console: Optional[Console] = None):
        """
        Initialize progress manager.
        
        Args:
            console: Rich console instance, creates new one if None
        """
        self.console = console or Console()
        self._active_progress: Optional[Progress] = None
        self._active_live: Optional[Live] = None
        self._spinner_thread: Optional[threading.Thread] = None
        self._stop_spinner = False
    
    @contextmanager
    def progress_bar(self, description: str = "Processing", total: Optional[int] = None):
        """
        Context manager for progress bar.
        
        Args:
            description: Description of the operation
            total: Total number of items to process
            
        Yields:
            Progress task that can be updated
        """
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        )
        
        with progress:
            task = progress.add_task(description, total=total)
            self._active_progress = progress
            try:
                yield task, progress
            finally:
                self._active_progress = None
    
    @contextmanager
    def spinner(self, message: str = "Working", spinner_style: str = "dots"):
        """
        Context manager for spinner indicator.
        
        Args:
            message: Message to display with spinner
            spinner_style: Style of spinner (dots, line, etc.)
        """
        spinner = Spinner(spinner_style, text=message)
        
        with Live(spinner, console=self.console, refresh_per_second=10):
            yield
    
    @contextmanager
    def thinking_indicator(self, message: str = "AI is thinking"):
        """
        Context manager for AI thinking indicator.
        
        Args:
            message: Message to display
        """
        thinking_text = Text()
        thinking_text.append("🤖 ", style="bold blue")
        thinking_text.append(message, style="italic")
        thinking_text.append("...", style="dim")
        
        spinner = Spinner("dots", text=thinking_text)
        
        with Live(spinner, console=self.console, refresh_per_second=8):
            yield
    
    @contextmanager
    def execution_indicator(self, command: str):
        """
        Context manager for command execution indicator.
        
        Args:
            command: Command being executed
        """
        exec_text = Text()
        exec_text.append("⚡ ", style="bold yellow")
        exec_text.append("Executing: ", style="bold")
        exec_text.append(command[:50] + "..." if len(command) > 50 else command, style="cyan")
        
        spinner = Spinner("line", text=exec_text)
        
        with Live(spinner, console=self.console, refresh_per_second=12):
            yield
    
    @contextmanager
    def file_operation_indicator(self, operation: str, file_path: str):
        """
        Context manager for file operation indicator.
        
        Args:
            operation: Type of operation (reading, writing, etc.)
            file_path: Path of file being operated on
        """
        file_text = Text()
        file_text.append("📁 ", style="bold green")
        file_text.append(f"{operation.title()}: ", style="bold")
        file_text.append(file_path, style="cyan")
        
        spinner = Spinner("dots2", text=file_text)
        
        with Live(spinner, console=self.console, refresh_per_second=10):
            yield
    
    def update_progress(self, task_id: Any, advance: int = 1, description: Optional[str] = None):
        """
        Update progress for a task.
        
        Args:
            task_id: Task identifier
            advance: Amount to advance progress
            description: New description for the task
        """
        if self._active_progress:
            self._active_progress.update(task_id, advance=advance)
            if description:
                self._active_progress.update(task_id, description=description)
    
    def set_progress(self, task_id: Any, completed: int, description: Optional[str] = None):
        """
        Set absolute progress for a task.
        
        Args:
            task_id: Task identifier
            completed: Number of completed items
            description: New description for the task
        """
        if self._active_progress:
            self._active_progress.update(task_id, completed=completed)
            if description:
                self._active_progress.update(task_id, description=description)
    
    def start_background_spinner(self, message: str = "Working", spinner_style: str = "dots"):
        """
        Start a background spinner that runs in a separate thread.
        
        Args:
            message: Message to display
            spinner_style: Style of spinner
        """
        if self._spinner_thread and self._spinner_thread.is_alive():
            self.stop_background_spinner()
        
        self._stop_spinner = False
        
        def spinner_worker():
            spinner = Spinner(spinner_style, text=message)
            with Live(spinner, console=self.console, refresh_per_second=10):
                while not self._stop_spinner:
                    time.sleep(0.1)
        
        self._spinner_thread = threading.Thread(target=spinner_worker, daemon=True)
        self._spinner_thread.start()
    
    def stop_background_spinner(self):
        """Stop the background spinner."""
        self._stop_spinner = True
        if self._spinner_thread:
            self._spinner_thread.join(timeout=1.0)
            self._spinner_thread = None
    
    def show_completion(self, message: str, success: bool = True):
        """
        Show completion message.
        
        Args:
            message: Completion message
            success: Whether operation was successful
        """
        if success:
            self.console.print(f"✅ {message}", style="bold green")
        else:
            self.console.print(f"❌ {message}", style="bold red")
    
    def show_warning(self, message: str):
        """
        Show warning message.
        
        Args:
            message: Warning message
        """
        self.console.print(f"⚠️  {message}", style="bold yellow")
    
    def show_info(self, message: str):
        """
        Show info message.
        
        Args:
            message: Info message
        """
        self.console.print(f"ℹ️  {message}", style="bold blue")


# Global progress manager instance
_progress_manager: Optional[ProgressManager] = None


def get_progress_manager() -> ProgressManager:
    """Get the global progress manager instance."""
    global _progress_manager
    if _progress_manager is None:
        _progress_manager = ProgressManager()
    return _progress_manager


# Convenience functions
def show_progress(description: str = "Processing", total: Optional[int] = None):
    """Convenience function to get progress bar context manager."""
    return get_progress_manager().progress_bar(description, total)


def show_spinner(message: str = "Working", spinner_style: str = "dots"):
    """Convenience function to get spinner context manager."""
    return get_progress_manager().spinner(message, spinner_style)


def show_thinking(message: str = "AI is thinking"):
    """Convenience function to get thinking indicator context manager."""
    return get_progress_manager().thinking_indicator(message)


def show_execution(command: str):
    """Convenience function to get execution indicator context manager."""
    return get_progress_manager().execution_indicator(command)


def show_file_operation(operation: str, file_path: str):
    """Convenience function to get file operation indicator context manager."""
    return get_progress_manager().file_operation_indicator(operation, file_path)
