"""
Rich terminal user interface for Kritrima AI CLI.

Provides beautiful, interactive terminal interface components.
"""

import time
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Iterator

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.syntax import Syntax
from rich.markdown import Markdown

from ..config import get_config


class TerminalUI:
    """
    Rich terminal user interface manager.
    
    Provides context managers and utilities for displaying progress,
    status updates, and formatted content in the terminal.
    """
    
    def __init__(self):
        """Initialize terminal UI."""
        self.config = get_config()
        self.console = Console()
    
    @contextmanager
    def show_thinking(self) -> Iterator[None]:
        """
        Context manager for showing thinking/processing indicator.
        
        Usage:
            with ui.show_thinking():
                # Do some processing
                pass
        """
        if not self.config.show_progress:
            yield
            return
        
        thinking_text = Text()
        thinking_text.append("🤖 ", style="bold blue")
        thinking_text.append("Thinking", style="bold cyan")
        thinking_text.append("...", style="bold cyan blink")
        
        panel = Panel(thinking_text, border_style="blue", padding=(0, 2))
        
        with Live(panel, refresh_per_second=2) as live:
            yield
    
    @contextmanager
    def show_execution(self, tool_name: str) -> Iterator[None]:
        """
        Context manager for showing tool execution progress.
        
        Args:
            tool_name: Name of the tool being executed
            
        Usage:
            with ui.show_execution("shell"):
                # Execute tool
                pass
        """
        if not self.config.show_progress:
            yield
            return
        
        execution_text = Text()
        execution_text.append("⚙️  ", style="bold yellow")
        execution_text.append(f"Executing {tool_name}", style="bold yellow")
        execution_text.append("...", style="bold yellow blink")
        
        panel = Panel(execution_text, border_style="yellow", padding=(0, 2))
        
        with Live(panel, refresh_per_second=2) as live:
            yield
    
    @contextmanager
    def show_progress(
        self,
        description: str = "Processing...",
        total: Optional[int] = None
    ) -> Iterator[Progress]:
        """
        Context manager for showing progress bar.
        
        Args:
            description: Progress description
            total: Total number of steps (None for indeterminate)
            
        Usage:
            with ui.show_progress("Loading files", 100) as progress:
                task = progress.add_task("Loading", total=100)
                for i in range(100):
                    progress.update(task, advance=1)
        """
        if not self.config.show_progress:
            # Return a dummy progress object
            class DummyProgress:
                def add_task(self, *args, **kwargs): return 0
                def update(self, *args, **kwargs): pass
                def remove_task(self, *args, **kwargs): pass
            
            yield DummyProgress()
            return
        
        if total is None:
            # Indeterminate progress (spinner only)
            columns = [
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
            ]
        else:
            # Determinate progress (with bar)
            columns = [
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TaskProgressColumn(),
            ]
        
        with Progress(*columns, console=self.console) as progress:
            yield progress
    
    def display_code(
        self,
        code: str,
        language: str = "python",
        title: Optional[str] = None,
        line_numbers: bool = True
    ) -> None:
        """
        Display syntax-highlighted code.
        
        Args:
            code: Code to display
            language: Programming language for syntax highlighting
            title: Optional title for the code block
            line_numbers: Whether to show line numbers
        """
        syntax = Syntax(
            code,
            language,
            theme="monokai",
            line_numbers=line_numbers,
            word_wrap=True
        )
        
        if title:
            panel = Panel(syntax, title=title, border_style="blue")
            self.console.print(panel)
        else:
            self.console.print(syntax)
    
    def display_markdown(self, markdown_text: str) -> None:
        """
        Display formatted markdown text.
        
        Args:
            markdown_text: Markdown text to display
        """
        markdown = Markdown(markdown_text)
        self.console.print(markdown)
    
    def display_table(
        self,
        data: List[Dict[str, Any]],
        title: Optional[str] = None,
        headers: Optional[List[str]] = None
    ) -> None:
        """
        Display data in a formatted table.
        
        Args:
            data: List of dictionaries representing table rows
            title: Optional table title
            headers: Optional custom headers (uses dict keys if not provided)
        """
        if not data:
            self.console.print("[dim]No data to display[/dim]")
            return
        
        # Determine headers
        if headers is None:
            headers = list(data[0].keys())
        
        # Create table
        table = Table(title=title, show_header=True, header_style="bold magenta")
        
        # Add columns
        for header in headers:
            table.add_column(header, style="cyan", no_wrap=False)
        
        # Add rows
        for row in data:
            table.add_row(*[str(row.get(header, "")) for header in headers])
        
        self.console.print(table)
    
    def display_json(
        self,
        data: Dict[str, Any],
        title: Optional[str] = None,
        indent: int = 2
    ) -> None:
        """
        Display JSON data with syntax highlighting.
        
        Args:
            data: Dictionary to display as JSON
            title: Optional title
            indent: JSON indentation level
        """
        import json
        
        json_str = json.dumps(data, indent=indent, ensure_ascii=False)
        self.display_code(json_str, language="json", title=title)
    
    def display_error(
        self,
        error_message: str,
        title: str = "Error",
        suggestions: Optional[List[str]] = None
    ) -> None:
        """
        Display error message with optional suggestions.
        
        Args:
            error_message: Error message to display
            title: Error title
            suggestions: Optional list of suggestions
        """
        error_text = Text()
        error_text.append("❌ ", style="bold red")
        error_text.append(error_message, style="red")
        
        if suggestions:
            error_text.append("\n\nSuggestions:", style="bold yellow")
            for suggestion in suggestions:
                error_text.append(f"\n  • {suggestion}", style="yellow")
        
        panel = Panel(error_text, title=title, border_style="red", padding=(1, 2))
        self.console.print(panel)
    
    def display_warning(
        self,
        warning_message: str,
        title: str = "Warning"
    ) -> None:
        """
        Display warning message.
        
        Args:
            warning_message: Warning message to display
            title: Warning title
        """
        warning_text = Text()
        warning_text.append("⚠️  ", style="bold yellow")
        warning_text.append(warning_message, style="yellow")
        
        panel = Panel(warning_text, title=title, border_style="yellow", padding=(1, 2))
        self.console.print(panel)
    
    def display_success(
        self,
        success_message: str,
        title: str = "Success"
    ) -> None:
        """
        Display success message.
        
        Args:
            success_message: Success message to display
            title: Success title
        """
        success_text = Text()
        success_text.append("✅ ", style="bold green")
        success_text.append(success_message, style="green")
        
        panel = Panel(success_text, title=title, border_style="green", padding=(1, 2))
        self.console.print(panel)
    
    def display_info(
        self,
        info_message: str,
        title: str = "Info"
    ) -> None:
        """
        Display info message.
        
        Args:
            info_message: Info message to display
            title: Info title
        """
        info_text = Text()
        info_text.append("ℹ️  ", style="bold blue")
        info_text.append(info_message, style="blue")
        
        panel = Panel(info_text, title=title, border_style="blue", padding=(1, 2))
        self.console.print(panel)
    
    def clear_screen(self) -> None:
        """Clear the terminal screen."""
        self.console.clear()
    
    def print_separator(self, char: str = "─", style: str = "dim") -> None:
        """
        Print a separator line.
        
        Args:
            char: Character to use for separator
            style: Rich style for the separator
        """
        width = self.console.size.width
        separator = char * width
        self.console.print(separator, style=style)
    
    def get_terminal_size(self) -> tuple[int, int]:
        """
        Get terminal size.
        
        Returns:
            Tuple of (width, height)
        """
        size = self.console.size
        return size.width, size.height
    
    def is_terminal_interactive(self) -> bool:
        """Check if terminal is interactive."""
        return self.console.is_terminal
    
    def set_color_output(self, enabled: bool) -> None:
        """
        Enable or disable color output.
        
        Args:
            enabled: Whether to enable color output
        """
        self.console._color_system = "auto" if enabled else None
