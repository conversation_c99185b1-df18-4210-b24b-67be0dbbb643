"""
Basic tests for Kritrima AI CLI.

Tests core functionality and imports to ensure the system is working correctly.
"""

import pytest
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_imports():
    """Test that all core modules can be imported."""
    try:
        import kritrima
        from kritrima import __version__
        from kritrima.config import Config
        from kritrima.session import Session
        from kritrima.agent_engine import AgentEngine
        from kritrima.ai.providers import get_provider
        from kritrima.ai.tools import get_tool_registry
        from kritrima.execution.shell_executor import ShellExecutor
        from kritrima.execution.file_operations import FileOperations
        from kritrima.safety.security import SecurityValidator
        
        assert __version__ is not None
        assert len(__version__) > 0
        
    except ImportError as e:
        pytest.fail(f"Failed to import core modules: {e}")


def test_config_creation():
    """Test configuration creation."""
    from kritrima.config import Config
    
    config = Config()
    assert config.debug is not None
    assert config.default_provider is not None
    assert config.approval_mode is not None


def test_session_creation():
    """Test session creation."""
    from kritrima.session import Session
    
    session = Session()
    assert session.session_id is not None
    assert len(session.session_id) > 0
    assert session.working_directory is not None


def test_security_validator():
    """Test security validator."""
    from kritrima.safety.security import SecurityValidator, SecurityLevel
    
    validator = SecurityValidator()
    
    # Test safe command
    result = validator.validate_command("ls -la")
    assert result.is_safe is True
    assert result.level == SecurityLevel.LOW
    
    # Test dangerous command
    result = validator.validate_command("rm -rf /")
    assert result.is_safe is False
    assert result.level == SecurityLevel.CRITICAL


def test_tool_registry():
    """Test tool registry."""
    from kritrima.ai.tools import get_tool_registry
    
    registry = get_tool_registry()
    tools = registry.list_tools()
    
    assert len(tools) > 0
    assert "shell" in tools
    assert "file_read" in tools
    assert "file_write" in tools


def test_file_operations():
    """Test file operations."""
    import tempfile
    import asyncio
    from kritrima.execution.file_operations import FileOperations
    
    async def run_test():
        file_ops = FileOperations()
        
        # Test with temporary file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("Hello, Kritrima!")
            temp_path = f.name
        
        try:
            # Test read
            result = await file_ops.read_file(temp_path)
            assert result.success is True
            assert "Hello, Kritrima!" in result.content
            
            # Test write
            result = await file_ops.write_file(temp_path, "Updated content", mode="write")
            assert result.success is True
            
            # Test read updated content
            result = await file_ops.read_file(temp_path)
            assert result.success is True
            assert "Updated content" in result.content
            
        finally:
            # Clean up
            Path(temp_path).unlink(missing_ok=True)
    
    asyncio.run(run_test())


def test_shell_executor():
    """Test shell executor with safe commands."""
    import asyncio
    from kritrima.execution.shell_executor import ShellExecutor
    
    async def run_test():
        executor = ShellExecutor()
        
        # Test safe command
        result = await executor.execute("echo 'Hello, World!'")
        assert result.success is True
        assert "Hello, World!" in result.stdout
        
        # Test command with working directory
        result = await executor.execute("pwd")
        assert result.success is True
        assert result.stdout.strip() != ""
    
    asyncio.run(run_test())


def test_cli_entry_point():
    """Test CLI entry point exists."""
    from kritrima.cli import main
    
    # Just test that the function exists and is callable
    assert callable(main)


def test_version_consistency():
    """Test version consistency across files."""
    from kritrima import __version__
    
    # Read version from pyproject.toml
    pyproject_path = Path(__file__).parent.parent / "pyproject.toml"
    if pyproject_path.exists():
        import toml
        pyproject_data = toml.load(pyproject_path)
        pyproject_version = pyproject_data["tool"]["poetry"]["version"]
        assert __version__ == pyproject_version


if __name__ == "__main__":
    pytest.main([__file__])
