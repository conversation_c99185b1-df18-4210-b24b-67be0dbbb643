"""
Security validation for command execution and file operations.

Provides comprehensive security checks to prevent dangerous operations.
"""

import re
import os
import platform
from pathlib import Path
from typing import List, Set, Pattern, Dict, Any
from dataclasses import dataclass
from enum import Enum

from ..config import get_config


class SecurityLevel(str, Enum):
    """Security levels for validation."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class SecurityResult:
    """Result of security validation."""
    is_safe: bool
    level: SecurityLevel
    reason: str
    suggestions: List[str] = None
    
    def __post_init__(self):
        if self.suggestions is None:
            self.suggestions = []


class SecurityValidator:
    """
    Comprehensive security validator for commands and operations.
    
    Validates commands against known dangerous patterns, file system access,
    and system-level operations to prevent harmful actions.
    """
    
    def __init__(self):
        """Initialize security validator."""
        self.config = get_config()
        self._initialize_patterns()
    
    def _initialize_patterns(self) -> None:
        """Initialize security patterns and rules."""
        # Critical dangerous commands (immediate block)
        self.critical_patterns = [
            # System destruction
            r'\b(rm\s+(-rf?|--recursive|--force).*/)|\brm\s+-rf?\s+/\b',
            r'\bformat\s+[a-zA-Z]:\b',
            r'\bfdisk\b.*\b/dev/\b',
            r'\bdd\s+if=.*of=/dev/\b',
            r'\bmkfs\b',
            r'\bshred\b.*\b/dev/\b',
            
            # System control
            r'\b(shutdown|reboot|halt|poweroff)\b',
            r'\bsudo\s+(rm|format|fdisk|dd|mkfs)\b',
            r'\bsu\s+-.*\b(rm|format)\b',
            
            # Network attacks
            r'\b(nmap|masscan|zmap)\b.*\b(-sS|-sT|-sU)\b',
            r'\bhping3?\b.*\b--flood\b',
            r'\bping\b.*\b-f\b',
            
            # Malware-like behavior
            r'\bcurl\b.*\|\s*sh\b',
            r'\bwget\b.*\|\s*sh\b',
            r'\bchmod\s+777\s+/\b',
            r'\bchown\s+.*\s+/\b',
        ]
        
        # High-risk patterns (require approval)
        self.high_risk_patterns = [
            # File system modifications
            r'\brm\s+.*\.(py|js|ts|java|cpp|c|h)$',
            r'\bmv\s+.*\s+/dev/null\b',
            r'\b>\s*/dev/null\b.*\b2>&1\b',
            
            # System modifications
            r'\bchmod\s+[0-7]{3,4}\b',
            r'\bchown\s+\w+:\w+\b',
            r'\buseradd\b|\buserdel\b|\busermod\b',
            r'\bpasswd\b',
            
            # Network operations
            r'\bnc\b.*\b-l\b',  # netcat listener
            r'\btelnet\b',
            r'\bftp\b',
            r'\bssh\b.*\b-R\b',  # SSH reverse tunnel
            
            # Package management
            r'\b(apt|yum|dnf|pacman|brew)\s+(remove|uninstall)\b',
            r'\bpip\s+uninstall\b',
            r'\bnpm\s+(uninstall|remove)\b',
            
            # Process control
            r'\bkill\s+-9\b',
            r'\bkillall\b',
            r'\bpkill\b',
        ]
        
        # Medium-risk patterns (warn but allow)
        self.medium_risk_patterns = [
            # File operations
            r'\brm\s+.*\*\b',
            r'\bmv\s+.*\s+.*\*\b',
            r'\bcp\s+-r\b',
            
            # System information
            r'\bps\s+aux\b',
            r'\bnetstat\b',
            r'\blsof\b',
            
            # Development operations
            r'\bgit\s+reset\s+--hard\b',
            r'\bgit\s+clean\s+-fd\b',
            r'\bnpm\s+install\b.*\b--global\b',
        ]
        
        # Safe commands (always allowed)
        self.safe_commands = {
            'ls', 'dir', 'pwd', 'cd', 'echo', 'cat', 'head', 'tail', 'less', 'more',
            'grep', 'find', 'locate', 'which', 'whereis', 'type', 'file', 'stat',
            'wc', 'sort', 'uniq', 'cut', 'awk', 'sed', 'tr', 'tee',
            'date', 'cal', 'uptime', 'whoami', 'id', 'groups',
            'git status', 'git log', 'git diff', 'git show', 'git branch',
            'npm list', 'npm info', 'npm view', 'pip list', 'pip show',
            'python --version', 'node --version', 'java -version',
            'docker ps', 'docker images', 'docker version',
            'kubectl get', 'kubectl describe', 'kubectl logs',
        }
        
        # Compile patterns for performance
        self.compiled_critical = [re.compile(p, re.IGNORECASE) for p in self.critical_patterns]
        self.compiled_high_risk = [re.compile(p, re.IGNORECASE) for p in self.high_risk_patterns]
        self.compiled_medium_risk = [re.compile(p, re.IGNORECASE) for p in self.medium_risk_patterns]
    
    def validate_command(self, command: str) -> SecurityResult:
        """
        Validate a command for security risks.
        
        Args:
            command: Command string to validate
            
        Returns:
            SecurityResult with validation details
        """
        command = command.strip()
        
        # Check if command is in safe list
        if self._is_safe_command(command):
            return SecurityResult(
                is_safe=True,
                level=SecurityLevel.LOW,
                reason="Command is in safe list"
            )
        
        # Check critical patterns
        for pattern in self.compiled_critical:
            if pattern.search(command):
                return SecurityResult(
                    is_safe=False,
                    level=SecurityLevel.CRITICAL,
                    reason=f"Command matches critical security pattern: {pattern.pattern}",
                    suggestions=[
                        "This command could cause irreversible system damage",
                        "Consider using safer alternatives",
                        "Review the command carefully before execution"
                    ]
                )
        
        # Check high-risk patterns
        for pattern in self.compiled_high_risk:
            if pattern.search(command):
                return SecurityResult(
                    is_safe=False,
                    level=SecurityLevel.HIGH,
                    reason=f"Command matches high-risk pattern: {pattern.pattern}",
                    suggestions=[
                        "This command requires careful review",
                        "Ensure you understand the implications",
                        "Consider testing in a safe environment first"
                    ]
                )
        
        # Check medium-risk patterns
        for pattern in self.compiled_medium_risk:
            if pattern.search(command):
                return SecurityResult(
                    is_safe=True,
                    level=SecurityLevel.MEDIUM,
                    reason=f"Command matches medium-risk pattern: {pattern.pattern}",
                    suggestions=[
                        "This command has some risk",
                        "Review the command parameters",
                        "Ensure you have backups if needed"
                    ]
                )
        
        # Additional checks
        security_issues = []
        
        # Check for suspicious file paths
        if self._has_suspicious_paths(command):
            security_issues.append("Command accesses sensitive system paths")
        
        # Check for privilege escalation
        if self._has_privilege_escalation(command):
            security_issues.append("Command attempts privilege escalation")
        
        # Check for network operations
        if self._has_network_operations(command):
            security_issues.append("Command performs network operations")
        
        if security_issues:
            return SecurityResult(
                is_safe=False,
                level=SecurityLevel.HIGH,
                reason="; ".join(security_issues),
                suggestions=[
                    "Review network and system access",
                    "Ensure proper authorization",
                    "Consider running in isolated environment"
                ]
            )
        
        # Default to safe with low risk
        return SecurityResult(
            is_safe=True,
            level=SecurityLevel.LOW,
            reason="No security issues detected"
        )
    
    def _is_safe_command(self, command: str) -> bool:
        """Check if command is in the safe list."""
        # Extract base command
        base_command = command.split()[0] if command.split() else ""
        
        # Check exact matches
        if command in self.safe_commands:
            return True
        
        # Check base command matches
        for safe_cmd in self.safe_commands:
            if command.startswith(safe_cmd):
                return True
        
        return False
    
    def _has_suspicious_paths(self, command: str) -> bool:
        """Check for access to suspicious file paths."""
        suspicious_paths = [
            '/etc/passwd', '/etc/shadow', '/etc/sudoers',
            '/boot/', '/sys/', '/proc/sys/',
            '~/.ssh/', '~/.aws/', '~/.config/',
            '/var/log/', '/var/run/', '/var/lib/',
            'C:\\Windows\\System32\\', 'C:\\Program Files\\',
            '/System/', '/Library/', '/Applications/'
        ]
        
        for path in suspicious_paths:
            if path in command:
                return True
        
        return False
    
    def _has_privilege_escalation(self, command: str) -> bool:
        """Check for privilege escalation attempts."""
        escalation_patterns = [
            r'\bsudo\b', r'\bsu\b', r'\brunas\b',
            r'\bchmod\s+[0-7]*[4-7][0-7]*\b',  # SUID/SGID
            r'\bsetuid\b', r'\bsetgid\b',
            r'\bvisudo\b', r'\bsudoedit\b'
        ]
        
        for pattern in escalation_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def _has_network_operations(self, command: str) -> bool:
        """Check for network operations."""
        network_patterns = [
            r'\bcurl\b.*\bhttp[s]?://\b',
            r'\bwget\b.*\bhttp[s]?://\b',
            r'\bnc\b.*\b-l\b',
            r'\btelnet\b.*\b\d+\.\d+\.\d+\.\d+\b',
            r'\bssh\b.*\b\d+\.\d+\.\d+\.\d+\b',
            r'\bftp\b.*\b\d+\.\d+\.\d+\.\d+\b',
            r'\bnmap\b', r'\bmasscan\b', r'\bzmap\b'
        ]
        
        for pattern in network_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def validate_file_path(self, file_path: str, operation: str = "read") -> SecurityResult:
        """
        Validate file path for security risks.
        
        Args:
            file_path: File path to validate
            operation: Type of operation (read, write, delete, execute)
            
        Returns:
            SecurityResult with validation details
        """
        try:
            path = Path(file_path).resolve()
        except Exception:
            return SecurityResult(
                is_safe=False,
                level=SecurityLevel.HIGH,
                reason="Invalid file path"
            )
        
        # Check for system-critical paths
        critical_paths = [
            '/etc/', '/boot/', '/sys/', '/proc/',
            '/var/run/', '/var/lib/', '/usr/bin/', '/usr/sbin/',
            'C:\\Windows\\', 'C:\\Program Files\\', 'C:\\System32\\'
        ]
        
        path_str = str(path)
        for critical_path in critical_paths:
            if path_str.startswith(critical_path):
                if operation in ['write', 'delete', 'execute']:
                    return SecurityResult(
                        is_safe=False,
                        level=SecurityLevel.CRITICAL,
                        reason=f"Attempting {operation} operation on system-critical path: {critical_path}"
                    )
                elif operation == 'read':
                    return SecurityResult(
                        is_safe=False,
                        level=SecurityLevel.HIGH,
                        reason=f"Reading from system-critical path: {critical_path}"
                    )
        
        # Check for hidden/config files
        if path.name.startswith('.') and operation in ['write', 'delete']:
            return SecurityResult(
                is_safe=False,
                level=SecurityLevel.MEDIUM,
                reason="Modifying hidden/configuration file",
                suggestions=["Ensure you understand the implications of modifying this file"]
            )
        
        return SecurityResult(
            is_safe=True,
            level=SecurityLevel.LOW,
            reason="File path appears safe"
        )
    
    def get_security_recommendations(self) -> List[str]:
        """Get general security recommendations."""
        return [
            "Always review commands before execution",
            "Use version control for important files",
            "Test dangerous operations in isolated environments",
            "Keep regular backups of important data",
            "Understand the implications of system-level commands",
            "Use least-privilege principles",
            "Monitor system logs for suspicious activity"
        ]
