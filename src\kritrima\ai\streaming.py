"""
Real-time streaming response handling for AI providers.

Provides a unified interface for handling streaming responses from different AI providers.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, AsyncIterator, Callable, Union
from dataclasses import dataclass, field
from enum import Enum

from openai.types.chat.chat_completion_chunk import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>hun<PERSON>, ChoiceDelta


class StreamEventType(str, Enum):
    """Types of streaming events."""
    CONTENT = "content"
    TOOL_CALL = "tool_call"
    FUNCTION_CALL = "function_call"  # Legacy support
    FINISH = "finish"
    ERROR = "error"


@dataclass
class StreamEvent:
    """A single event in the streaming response."""
    type: StreamEventType
    data: Any
    timestamp: float = field(default_factory=lambda: asyncio.get_event_loop().time())
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ToolCall:
    """Represents a tool call in the streaming response."""
    id: str
    type: str
    function: Dict[str, Any]

    @classmethod
    def from_delta(cls, delta: ChoiceDelta, call_id: str) -> "ToolCall":
        """Create ToolCall from streaming delta."""
        return cls(
            id=call_id,
            type="function",
            function={
                "name": delta.tool_calls[0].function.name if delta.tool_calls else "",
                "arguments": delta.tool_calls[0].function.arguments if delta.tool_calls else ""
            }
        )


class StreamingResponse:
    """
    Handles streaming responses from AI providers.

    Provides a unified interface for processing streaming chat completions,
    including content generation and tool calls.
    """

    def __init__(self, stream: AsyncIterator[ChatCompletionChunk]):
        """
        Initialize streaming response.

        Args:
            stream: Async iterator of chat completion chunks
        """
        self.stream = stream
        self.content_buffer = ""
        self.tool_calls: Dict[str, ToolCall] = {}
        self.current_tool_call_id: Optional[str] = None
        self.is_finished = False
        self.finish_reason: Optional[str] = None

        # Event handlers
        self._content_handlers: List[Callable[[str], None]] = []
        self._tool_call_handlers: List[Callable[[ToolCall], None]] = []
        self._finish_handlers: List[Callable[[str], None]] = []
        self._error_handlers: List[Callable[[Exception], None]] = []

    def on_content(self, handler: Callable[[str], None]) -> None:
        """Register a handler for content updates."""
        self._content_handlers.append(handler)

    def on_tool_call(self, handler: Callable[[ToolCall], None]) -> None:
        """Register a handler for tool calls."""
        self._tool_call_handlers.append(handler)

    def on_finish(self, handler: Callable[[str], None]) -> None:
        """Register a handler for stream completion."""
        self._finish_handlers.append(handler)

    def on_error(self, handler: Callable[[Exception], None]) -> None:
        """Register a handler for errors."""
        self._error_handlers.append(handler)
    
    async def process(self) -> AsyncIterator[StreamEvent]:
        """
        Process the streaming response and yield events.

        Yields:
            StreamEvent objects for each update
        """
        try:
            async for chunk in self.stream:
                if not chunk.choices:
                    continue

                choice = chunk.choices[0]
                delta = choice.delta

                # Handle content updates
                if delta.content:
                    self.content_buffer += delta.content
                    event = StreamEvent(
                        type=StreamEventType.CONTENT,
                        data=delta.content,
                        metadata={"total_content": self.content_buffer}
                    )

                    # Notify content handlers
                    for handler in self._content_handlers:
                        try:
                            handler(delta.content)
                        except Exception as e:
                            await self._handle_error(e)

                    yield event

                # Handle tool calls
                if delta.tool_calls:
                    for tool_call_delta in delta.tool_calls:
                        call_id = tool_call_delta.id or self.current_tool_call_id

                        if call_id:
                            if call_id not in self.tool_calls:
                                # New tool call
                                self.tool_calls[call_id] = ToolCall(
                                    id=call_id,
                                    type="function",
                                    function={"name": "", "arguments": ""}
                                )
                                self.current_tool_call_id = call_id

                            tool_call = self.tool_calls[call_id]

                            # Update function name
                            if tool_call_delta.function and tool_call_delta.function.name:
                                tool_call.function["name"] += tool_call_delta.function.name

                            # Update function arguments
                            if tool_call_delta.function and tool_call_delta.function.arguments:
                                tool_call.function["arguments"] += tool_call_delta.function.arguments

                            event = StreamEvent(
                                type=StreamEventType.TOOL_CALL,
                                data=tool_call,
                                metadata={"call_id": call_id, "is_complete": False}
                            )

                            yield event

                # Handle finish
                if choice.finish_reason:
                    self.finish_reason = choice.finish_reason
                    self.is_finished = True

                    # Complete any pending tool calls
                    for tool_call in self.tool_calls.values():
                        # Try to parse arguments as JSON
                        try:
                            tool_call.function["arguments"] = json.loads(tool_call.function["arguments"])
                        except (json.JSONDecodeError, TypeError):
                            # Keep as string if not valid JSON
                            pass

                        # Notify tool call handlers
                        for handler in self._tool_call_handlers:
                            try:
                                handler(tool_call)
                            except Exception as e:
                                await self._handle_error(e)

                    event = StreamEvent(
                        type=StreamEventType.FINISH,
                        data=choice.finish_reason,
                        metadata={
                            "content": self.content_buffer,
                            "tool_calls": list(self.tool_calls.values())
                        }
                    )

                    # Notify finish handlers
                    for handler in self._finish_handlers:
                        try:
                            handler(choice.finish_reason)
                        except Exception as e:
                            await self._handle_error(e)

                    yield event
                    break

        except Exception as e:
            await self._handle_error(e)
            yield StreamEvent(
                type=StreamEventType.ERROR,
                data=str(e),
                metadata={"exception": e}
            )

    async def _handle_error(self, error: Exception) -> None:
        """Handle errors during streaming."""
        for handler in self._error_handlers:
            try:
                handler(error)
            except Exception:
                pass  # Ignore errors in error handlers

    async def collect_content(self) -> str:
        """
        Collect all content from the stream.

        Returns:
            Complete content string
        """
        async for event in self.process():
            if event.type == StreamEventType.FINISH:
                break

        return self.content_buffer

    async def collect_tool_calls(self) -> List[ToolCall]:
        """
        Collect all tool calls from the stream.

        Returns:
            List of completed tool calls
        """
        async for event in self.process():
            if event.type == StreamEventType.FINISH:
                break

        return list(self.tool_calls.values())

    async def collect_all(self) -> Dict[str, Any]:
        """
        Collect all data from the stream.

        Returns:
            Dictionary with content, tool_calls, and finish_reason
        """
        async for event in self.process():
            if event.type == StreamEventType.FINISH:
                break

        return {
            "content": self.content_buffer,
            "tool_calls": list(self.tool_calls.values()),
            "finish_reason": self.finish_reason
        }

    def get_current_content(self) -> str:
        """Get current content buffer."""
        return self.content_buffer

    def get_current_tool_calls(self) -> List[ToolCall]:
        """Get current tool calls."""
        return list(self.tool_calls.values())

    def is_complete(self) -> bool:
        """Check if the stream is complete."""
        return self.is_finished


class StreamingManager:
    """
    Manages multiple streaming responses and provides utilities.
    """
    
    def __init__(self):
        """Initialize streaming manager."""
        self.active_streams: Dict[str, StreamingResponse] = {}
    
    def add_stream(self, stream_id: str, response: StreamingResponse) -> None:
        """Add a streaming response to management."""
        self.active_streams[stream_id] = response
    
    def remove_stream(self, stream_id: str) -> None:
        """Remove a streaming response from management."""
        self.active_streams.pop(stream_id, None)
    
    def get_stream(self, stream_id: str) -> Optional[StreamingResponse]:
        """Get a managed streaming response."""
        return self.active_streams.get(stream_id)
    
    async def wait_for_completion(self, stream_id: str, timeout: Optional[float] = None) -> bool:
        """
        Wait for a stream to complete.
        
        Args:
            stream_id: ID of the stream to wait for
            timeout: Maximum time to wait in seconds
            
        Returns:
            True if completed, False if timeout
        """
        stream = self.get_stream(stream_id)
        if not stream:
            return False
        
        try:
            if timeout:
                await asyncio.wait_for(
                    self._wait_for_stream_completion(stream),
                    timeout=timeout
                )
            else:
                await self._wait_for_stream_completion(stream)
            return True
        except asyncio.TimeoutError:
            return False
    
    async def _wait_for_stream_completion(self, stream: StreamingResponse) -> None:
        """Wait for a specific stream to complete."""
        async for event in stream.process():
            if event.type == StreamEventType.FINISH:
                break
    
    def cleanup_completed_streams(self) -> None:
        """Remove completed streams from management."""
        completed = [
            stream_id for stream_id, stream in self.active_streams.items()
            if stream.is_complete()
        ]
        
        for stream_id in completed:
            self.remove_stream(stream_id)
