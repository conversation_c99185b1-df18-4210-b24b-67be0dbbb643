"""
Safety and security module for Kritrima AI CLI.

Provides security validation, error handling, and retry logic.
"""

from .security import SecurityValidator, SecurityLevel, SecurityResult
from .error_handler import SafetyErrorHandler, get_safety_error_handler

# Alias for backward compatibility
ErrorHandler = SafetyErrorHandler

__all__ = [
    "SecurityValidator",
    "SecurityLevel",
    "SecurityResult",
    "SafetyErrorHandler",
    "get_safety_error_handler",
    "ErrorHandler",  # Backward compatibility
]
