"""
Main CLI entry point for Kritrima AI CLI.

Provides the command-line interface for the agentic AI-powered development assistant.
"""

import sys
import asyncio
from pathlib import Path
from typing import Optional, List

import click
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from . import __version__
from .config import Config, ApprovalMode, get_config, set_config
from .session import Session
from .agent_engine import AgentEngine
from .logging import get_logger, setup_logging, cleanup_old_logs
from .error_handling import get_error_handler
from .storage.context_manager import ContextManager


console = Console()


def print_banner() -> None:
    """Print the Kritrima AI CLI banner."""
    banner = Text()
    banner.append("🤖 ", style="bold blue")
    banner.append("Kritrima AI CLI", style="bold cyan")
    banner.append(f" v{__version__}", style="dim")
    banner.append("\n")
    banner.append("Agentic AI-Powered Development Assistant", style="italic")
    
    console.print(Panel(banner, border_style="blue", padding=(1, 2)))


@click.group(invoke_without_command=True)
@click.option('--version', is_flag=True, help='Show version and exit')
@click.option('--debug', is_flag=True, help='Enable debug mode')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose output')
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--provider', '-p', help='AI provider to use (openai, deepseek, ollama, azure)')
@click.option('--model', '-m', help='AI model to use')
@click.option('--mode', type=click.Choice(['suggest', 'auto-edit', 'full-auto']), 
              help='Approval mode for command execution')
@click.pass_context
def cli(
    ctx: click.Context,
    version: bool,
    debug: bool,
    verbose: bool,
    config: Optional[str],
    provider: Optional[str],
    model: Optional[str],
    mode: Optional[str]
) -> None:
    """
    Kritrima AI CLI - Agentic AI-Powered Development Assistant
    
    A production-ready AI assistant that lives in your local development environment.
    """
    if version:
        console.print(f"Kritrima AI CLI v{__version__}")
        sys.exit(0)
    
    # Load configuration
    config_obj = Config.load(Path(config) if config else None)
    
    # Override with command-line options
    if debug:
        config_obj.debug = True
    if verbose:
        config_obj.verbose = True
    if provider:
        config_obj.default_provider = provider
    if model:
        config_obj.default_model = model
    if mode:
        config_obj.approval_mode = ApprovalMode(mode)
    
    # Set global configuration
    set_config(config_obj)

    # Initialize logging
    setup_logging()

    # Clean up old logs if needed
    if not debug:
        try:
            cleanup_old_logs(max_age_days=30)
        except Exception:
            pass  # Don't fail if cleanup fails

    # Store in context for subcommands
    ctx.ensure_object(dict)
    ctx.obj['config'] = config_obj
    
    # If no subcommand, start interactive chat
    if ctx.invoked_subcommand is None:
        print_banner()
        asyncio.run(start_interactive_chat())


@cli.command()
@click.argument('prompt', required=False)
@click.option('--session', '-s', help='Session ID to use or resume')
@click.option('--new-session', is_flag=True, help='Start a new session')
@click.option('--working-dir', '-w', type=click.Path(exists=True), 
              help='Working directory for the session')
@click.pass_context
def chat(
    ctx: click.Context,
    prompt: Optional[str],
    session: Optional[str],
    new_session: bool,
    working_dir: Optional[str]
) -> None:
    """Start an interactive chat session or execute a single prompt."""
    asyncio.run(run_chat(prompt, session, new_session, working_dir))


@cli.command()
@click.argument('command')
@click.option('--working-dir', '-w', type=click.Path(exists=True),
              help='Working directory for command execution')
@click.option('--timeout', '-t', type=int, help='Command timeout in seconds')
@click.option('--approve', '-y', is_flag=True, help='Auto-approve command execution')
@click.pass_context
def execute(
    ctx: click.Context,
    command: str,
    working_dir: Optional[str],
    timeout: Optional[int],
    approve: bool
) -> None:
    """Execute a single command with AI assistance."""
    asyncio.run(run_execute(command, working_dir, timeout, approve))


@cli.command()
@click.option('--all', 'show_all', is_flag=True, help='Show all sessions')
@click.option('--limit', '-l', type=int, default=10, help='Limit number of sessions to show')
def sessions(show_all: bool, limit: int) -> None:
    """List and manage conversation sessions."""
    session_list = Session.list_sessions()
    
    if not session_list:
        console.print("No sessions found.", style="dim")
        return
    
    if not show_all:
        session_list = session_list[:limit]
    
    console.print(f"\n[bold]Recent Sessions[/bold] (showing {len(session_list)})")
    console.print()
    
    for i, session_info in enumerate(session_list, 1):
        session_id = session_info['session_id'][:8]
        working_dir = Path(session_info['working_directory']).name
        message_count = session_info['message_count']
        last_activity = session_info['last_activity'][:19]  # Remove timezone info
        
        console.print(f"{i:2d}. [cyan]{session_id}...[/cyan] "
                     f"[dim]({working_dir})[/dim] "
                     f"[green]{message_count} messages[/green] "
                     f"[dim]{last_activity}[/dim]")


@cli.command()
@click.argument('session_id')
@click.option('--confirm', is_flag=True, help='Skip confirmation prompt')
def delete_session(session_id: str, confirm: bool) -> None:
    """Delete a conversation session."""
    # Find session by partial ID
    session_list = Session.list_sessions()
    matching_sessions = [s for s in session_list if s['session_id'].startswith(session_id)]
    
    if not matching_sessions:
        console.print(f"No session found matching '{session_id}'", style="red")
        return
    
    if len(matching_sessions) > 1:
        console.print(f"Multiple sessions match '{session_id}'. Please be more specific:", style="yellow")
        for session_info in matching_sessions:
            short_id = session_info['session_id'][:8]
            console.print(f"  {short_id}...")
        return
    
    session_info = matching_sessions[0]
    full_session_id = session_info['session_id']
    
    if not confirm:
        if not click.confirm(f"Delete session {full_session_id[:8]}...?"):
            return
    
    # Load and delete session
    session = Session.load(full_session_id)
    if session and session.delete():
        console.print(f"Session {full_session_id[:8]}... deleted.", style="green")
    else:
        console.print(f"Failed to delete session {full_session_id[:8]}...", style="red")


@cli.command()
def config_show() -> None:
    """Show current configuration."""
    config_obj = get_config()
    
    console.print("\n[bold]Current Configuration[/bold]")
    console.print()
    
    # Core settings
    console.print(f"Debug mode: [cyan]{config_obj.debug}[/cyan]")
    console.print(f"Verbose output: [cyan]{config_obj.verbose}[/cyan]")
    console.print(f"Default provider: [cyan]{config_obj.default_provider}[/cyan]")
    console.print(f"Default model: [cyan]{config_obj.default_model}[/cyan]")
    console.print(f"Approval mode: [cyan]{config_obj.approval_mode}[/cyan]")
    console.print(f"Max context length: [cyan]{config_obj.max_context_length}[/cyan]")
    
    # Storage paths
    console.print(f"\nSession storage: [dim]{config_obj.session_storage_path}[/dim]")
    console.print(f"Cache path: [dim]{config_obj.cache_path}[/dim]")
    console.print(f"Log path: [dim]{config_obj.log_path}[/dim]")
    
    # Available providers
    console.print(f"\n[bold]Available Providers[/bold]")
    for name, provider in config_obj.providers.items():
        status = "[green]✓[/green]" if provider.api_key else "[red]✗[/red]"
        console.print(f"  {status} {name}: {provider.base_url}")


@cli.command()
@click.option('--provider', help='Test specific provider')
def test(provider: Optional[str]) -> None:
    """Test AI provider connections."""
    asyncio.run(run_test(provider))


@cli.command()
@click.option('--working-dir', '-w', type=click.Path(exists=True),
              help='Directory to analyze (default: current directory)')
@click.option('--detailed', '-d', is_flag=True, help='Show detailed information')
def info(working_dir: Optional[str], detailed: bool) -> None:
    """Show system and project information."""
    work_dir = Path(working_dir) if working_dir else Path.cwd()

    console.print(f"\n[bold]System & Project Information[/bold]")
    console.print(f"[dim]Directory: {work_dir}[/dim]\n")

    # Context information
    context_manager = ContextManager(work_dir)
    context = context_manager.get_context()

    # Project information
    console.print("[bold cyan]Project Context[/bold cyan]")
    console.print(f"  Type: [green]{context.project_type or 'Unknown'}[/green]")
    console.print(f"  Working Directory: [cyan]{context.working_directory}[/cyan]")

    if context.git_repository:
        console.print(f"  Git Repository: [green]✓[/green] {context.git_repository}")
        console.print(f"  Current Branch: [yellow]{context.git_branch}[/yellow]")
    else:
        console.print(f"  Git Repository: [red]✗[/red]")

    if context.python_version:
        console.print(f"  Python Version: [green]{context.python_version}[/green]")

    if context.package_files:
        console.print(f"  Package Files: [cyan]{', '.join(context.package_files)}[/cyan]")

    if detailed:
        # System information
        asyncio.run(show_detailed_system_info())

    console.print()


@cli.command()
@click.option('--tool-type', help='Filter by tool type (shell, file, system)')
@click.option('--dangerous-only', is_flag=True, help='Show only dangerous tools')
@click.option('--safe-only', is_flag=True, help='Show only safe tools')
def tools(tool_type: Optional[str], dangerous_only: bool, safe_only: bool) -> None:
    """List available tools and their capabilities."""
    from .ai.tools import get_tool_registry, ToolType

    registry = get_tool_registry()

    console.print(f"\n[bold]Available Tools[/bold]\n")

    # Filter tools
    if tool_type:
        try:
            filter_type = ToolType(tool_type)
            tools = registry.list_tools(filter_type)
        except ValueError:
            console.print(f"Invalid tool type: {tool_type}", style="red")
            return
    else:
        tools = registry.list_tools()

    if dangerous_only:
        tools = registry.get_dangerous_tools()
    elif safe_only:
        tools = registry.get_safe_tools()

    # Display tools
    for tool_name in sorted(tools):
        tool = registry.get_tool(tool_name)
        if tool:
            # Status indicators
            dangerous_indicator = "⚠️" if tool.schema.dangerous else "✅"
            approval_indicator = "🔒" if tool.schema.requires_approval else "🔓"

            console.print(f"{dangerous_indicator} {approval_indicator} [cyan]{tool_name}[/cyan]")
            console.print(f"    {tool.schema.description}")
            console.print(f"    Type: [dim]{tool.schema.tool_type.value}[/dim]")
            console.print()

    console.print(f"Total tools: [cyan]{len(tools)}[/cyan]")


@cli.command()
@click.option('--max-age', type=int, default=30, help='Maximum age of logs to keep (days)')
def cleanup(max_age: int) -> None:
    """Clean up old logs and temporary files."""
    console.print("🧹 Cleaning up old files...")

    try:
        cleanup_old_logs(max_age_days=max_age)
        console.print(f"✅ Cleaned up logs older than {max_age} days")
    except Exception as e:
        console.print(f"❌ Failed to clean up logs: {e}", style="red")

    # Clean up temporary session files
    config_obj = get_config()
    temp_files_cleaned = 0

    try:
        for temp_file in config_obj.session_storage_path.glob("*.tmp"):
            temp_file.unlink()
            temp_files_cleaned += 1

        if temp_files_cleaned > 0:
            console.print(f"✅ Cleaned up {temp_files_cleaned} temporary files")
        else:
            console.print("✅ No temporary files to clean")

    except Exception as e:
        console.print(f"❌ Failed to clean up temporary files: {e}", style="red")


async def show_detailed_system_info() -> None:
    """Show detailed system information."""
    from .ai.tools import SystemInfoTool, ProcessListTool

    console.print("\n[bold cyan]System Information[/bold cyan]")

    # Get system info
    system_tool = SystemInfoTool()
    result = await system_tool.execute(info_type="all")

    if result["success"]:
        system_info = result["system_info"]

        # OS Information
        if "os" in system_info:
            os_info = system_info["os"]
            console.print(f"  OS: [green]{os_info['system']} {os_info['release']}[/green]")
            console.print(f"  Architecture: [cyan]{os_info['architecture'][0]}[/cyan]")
            console.print(f"  Hostname: [cyan]{os_info['hostname']}[/cyan]")

        # CPU Information
        if "cpu" in system_info:
            cpu_info = system_info["cpu"]
            console.print(f"  CPU Cores: [cyan]{cpu_info['physical_cores']} physical, {cpu_info['total_cores']} total[/cyan]")
            console.print(f"  CPU Usage: [yellow]{cpu_info['cpu_usage']}%[/yellow]")

        # Memory Information
        if "memory" in system_info:
            memory_info = system_info["memory"]
            total_gb = memory_info['total'] / (1024**3)
            used_gb = memory_info['used'] / (1024**3)
            console.print(f"  Memory: [cyan]{used_gb:.1f}GB / {total_gb:.1f}GB ({memory_info['percentage']}%)[/cyan]")

        # Disk Information
        if "disk" in system_info:
            disk_info = system_info["disk"]
            total_gb = disk_info['total'] / (1024**3)
            used_gb = disk_info['used'] / (1024**3)
            console.print(f"  Disk: [cyan]{used_gb:.1f}GB / {total_gb:.1f}GB ({disk_info['percentage']:.1f}%)[/cyan]")

    # Top processes
    console.print("\n[bold cyan]Top Processes[/bold cyan]")
    process_tool = ProcessListTool()
    result = await process_tool.execute(limit=5)

    if result["success"]:
        for proc in result["processes"]:
            console.print(f"  [cyan]{proc['name']}[/cyan] (PID: {proc['pid']}, CPU: {proc['cpu_percent']}%)")


async def start_interactive_chat() -> None:
    """Start interactive chat mode."""
    config_obj = get_config()
    
    # Create or resume session
    session = Session(working_directory=Path.cwd())
    
    console.print(f"\n[dim]Session ID: {session.session_id[:8]}...[/dim]")
    console.print(f"[dim]Working directory: {session.working_directory}[/dim]")
    console.print(f"[dim]Type 'exit' or 'quit' to end the session[/dim]\n")
    
    # Initialize agent engine
    agent = AgentEngine(session=session)
    
    try:
        while True:
            # Get user input
            try:
                user_input = console.input("\n[bold blue]You:[/bold blue] ")
            except (EOFError, KeyboardInterrupt):
                break
            
            # Check for exit commands
            if user_input.lower().strip() in ['exit', 'quit', 'bye']:
                break
            
            if not user_input.strip():
                continue
            
            # Process with agent
            await agent.process_user_input(user_input)
    
    except KeyboardInterrupt:
        console.print("\n[dim]Session interrupted.[/dim]")
    
    finally:
        console.print(f"\n[dim]Session saved: {session.session_id[:8]}...[/dim]")


async def run_chat(
    prompt: Optional[str],
    session_id: Optional[str],
    new_session: bool,
    working_dir: Optional[str]
) -> None:
    """Run chat command."""
    # Determine working directory
    work_dir = Path(working_dir) if working_dir else Path.cwd()
    
    # Load or create session
    if session_id and not new_session:
        session = Session.load(session_id)
        if not session:
            console.print(f"Session '{session_id}' not found.", style="red")
            return
    else:
        session = Session(working_directory=work_dir)
    
    # Initialize agent
    agent = AgentEngine(session=session)
    
    if prompt:
        # Single prompt mode
        await agent.process_user_input(prompt)
    else:
        # Interactive mode
        print_banner()
        await start_interactive_chat()


async def run_execute(
    command: str,
    working_dir: Optional[str],
    timeout: Optional[int],
    approve: bool
) -> None:
    """Run execute command."""
    from .execution.shell_executor import ShellExecutor
    
    work_dir = Path(working_dir) if working_dir else Path.cwd()
    executor = ShellExecutor()
    
    console.print(f"[dim]Executing: {command}[/dim]")
    console.print(f"[dim]Working directory: {work_dir}[/dim]")
    
    if not approve:
        if not click.confirm("Execute this command?"):
            return
    
    result = await executor.execute(
        command=command,
        working_directory=work_dir,
        timeout=timeout
    )
    
    if result.success:
        console.print(f"[green]✓[/green] Command completed successfully")
        if result.stdout:
            console.print(result.stdout)
    else:
        console.print(f"[red]✗[/red] Command failed (exit code: {result.return_code})")
        if result.stderr:
            console.print(result.stderr, style="red")
        if result.error_message:
            console.print(f"Error: {result.error_message}", style="red")


async def run_test(provider: Optional[str]) -> None:
    """Test AI provider connections."""
    from .ai.providers import get_provider, list_available_providers
    
    config_obj = get_config()
    providers_to_test = [provider] if provider else list_available_providers()
    
    console.print("\n[bold]Testing AI Provider Connections[/bold]\n")
    
    for provider_name in providers_to_test:
        try:
            ai_provider = get_provider(provider_name)
            console.print(f"Testing {provider_name}... ", end="")
            
            # Simple test message
            messages = [{"role": "user", "content": "Hello, respond with just 'OK'"}]
            response = await ai_provider.chat_completion(messages=messages)
            
            console.print("[green]✓ Connected[/green]")
            
        except Exception as e:
            console.print(f"[red]✗ Failed: {str(e)}[/red]")


def main() -> None:
    """Main entry point."""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[dim]Interrupted.[/dim]")
        sys.exit(1)
    except Exception as e:
        console.print(f"\n[red]Error: {str(e)}[/red]")
        sys.exit(1)


if __name__ == '__main__':
    main()
