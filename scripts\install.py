#!/usr/bin/env python3
"""
Installation script for Kritrima AI CLI.

This script sets up the Kritrima AI CLI system with all dependencies
and configuration files.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path
from typing import List, Optional


def run_command(command: List[str], check: bool = True) -> subprocess.CompletedProcess:
    """Run a command and return the result."""
    print(f"Running: {' '.join(command)}")
    return subprocess.run(command, check=check, capture_output=True, text=True)


def check_python_version() -> bool:
    """Check if Python version is compatible."""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True


def check_poetry() -> bool:
    """Check if Poetry is installed."""
    try:
        result = run_command(["poetry", "--version"], check=False)
        if result.returncode == 0:
            print(f"✅ Poetry found: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Poetry not found")
    return False


def install_poetry() -> bool:
    """Install Poetry package manager."""
    print("Installing Poetry...")
    
    try:
        if platform.system() == "Windows":
            # Windows installation
            result = run_command([
                "powershell", "-Command",
                "(Invoke-WebRequest -Uri https://install.python-poetry.org -UseBasicParsing).Content | python -"
            ])
        else:
            # Unix-like systems
            result = run_command([
                "curl", "-sSL", "https://install.python-poetry.org", "|", "python3", "-"
            ])
        
        if result.returncode == 0:
            print("✅ Poetry installed successfully")
            return True
        else:
            print(f"❌ Poetry installation failed: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"❌ Poetry installation failed: {e}")
        return False


def install_dependencies() -> bool:
    """Install project dependencies."""
    print("Installing dependencies...")
    
    try:
        # Try Poetry first
        if check_poetry():
            result = run_command(["poetry", "install"])
            if result.returncode == 0:
                print("✅ Dependencies installed with Poetry")
                return True
        
        # Fallback to pip
        print("Falling back to pip installation...")
        result = run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        if result.returncode == 0:
            print("✅ Dependencies installed with pip")
            return True
        else:
            print(f"❌ Dependency installation failed: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"❌ Dependency installation failed: {e}")
        return False


def create_directories() -> bool:
    """Create necessary directories."""
    print("Creating directories...")
    
    directories = [
        Path.home() / ".kritrima",
        Path.home() / ".kritrima" / "sessions",
        Path.home() / ".kritrima" / "cache",
        Path.home() / ".kritrima" / "logs",
    ]
    
    try:
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"✅ Created: {directory}")
        return True
    
    except Exception as e:
        print(f"❌ Directory creation failed: {e}")
        return False


def copy_config_files() -> bool:
    """Copy default configuration files."""
    print("Setting up configuration...")
    
    try:
        config_dir = Path.home() / ".kritrima"
        source_config = Path("config") / "kritrima.yaml"
        dest_config = config_dir / "config.yaml"
        
        if source_config.exists() and not dest_config.exists():
            import shutil
            shutil.copy2(source_config, dest_config)
            print(f"✅ Configuration copied to: {dest_config}")
        else:
            print("ℹ️  Configuration already exists or source not found")
        
        return True
    
    except Exception as e:
        print(f"❌ Configuration setup failed: {e}")
        return False


def install_cli_command() -> bool:
    """Install CLI command globally."""
    print("Installing CLI command...")
    
    try:
        # Try Poetry first
        if check_poetry():
            result = run_command(["poetry", "install"])
            if result.returncode == 0:
                print("✅ CLI command installed with Poetry")
                print("You can now use 'kritrima' or 'kai' commands")
                return True
        
        # Fallback to pip
        result = run_command([sys.executable, "-m", "pip", "install", "-e", "."])
        if result.returncode == 0:
            print("✅ CLI command installed with pip")
            print("You can now use 'kritrima' or 'kai' commands")
            return True
        else:
            print(f"❌ CLI installation failed: {result.stderr}")
            return False
    
    except Exception as e:
        print(f"❌ CLI installation failed: {e}")
        return False


def check_optional_dependencies() -> None:
    """Check for optional dependencies."""
    print("\nChecking optional dependencies...")
    
    optional_deps = {
        "git": "Git version control",
        "docker": "Docker containerization",
        "node": "Node.js runtime",
        "npm": "Node.js package manager",
    }
    
    for command, description in optional_deps.items():
        try:
            result = run_command([command, "--version"], check=False)
            if result.returncode == 0:
                version = result.stdout.strip().split('\n')[0]
                print(f"✅ {description}: {version}")
            else:
                print(f"⚠️  {description}: Not found")
        except FileNotFoundError:
            print(f"⚠️  {description}: Not found")


def print_next_steps() -> None:
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 Kritrima AI CLI Installation Complete!")
    print("="*60)
    
    print("\nNext steps:")
    print("1. Set up your AI provider API keys:")
    print("   export OPENAI_API_KEY='your-openai-api-key'")
    print("   export DEEPSEEK_API_KEY='your-deepseek-api-key'")
    
    print("\n2. Test the installation:")
    print("   kritrima --version")
    print("   kritrima test")
    
    print("\n3. Start using Kritrima AI:")
    print("   kritrima chat")
    print("   kai 'Create a Python script to list files'")
    
    print("\n4. Get help:")
    print("   kritrima --help")
    print("   kritrima config-show")
    
    print("\nConfiguration file: ~/.kritrima/config.yaml")
    print("Documentation: https://docs.kritrima.ai")
    print("\nHappy coding! 🚀")


def main() -> None:
    """Main installation function."""
    print("🤖 Kritrima AI CLI Installation")
    print("=" * 40)
    
    # Check requirements
    if not check_python_version():
        sys.exit(1)
    
    # Install Poetry if needed
    if not check_poetry():
        if not install_poetry():
            print("⚠️  Continuing without Poetry (will use pip)")
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Create directories
    if not create_directories():
        print("❌ Failed to create directories")
        sys.exit(1)
    
    # Copy configuration
    if not copy_config_files():
        print("❌ Failed to setup configuration")
        sys.exit(1)
    
    # Install CLI command
    if not install_cli_command():
        print("❌ Failed to install CLI command")
        sys.exit(1)
    
    # Check optional dependencies
    check_optional_dependencies()
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    main()
