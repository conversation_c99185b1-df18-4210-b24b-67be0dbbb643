"""
Central agent engine for Kritrima AI CLI.

Orchestrates the entire AI-powered workflow including conversation management,
tool execution, streaming responses, and safety systems.
"""

import asyncio
import json
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum

from rich.console import Console
from rich.live import Live
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn

from .config import get_config, ApprovalMode
from .session import Session, MessageRole
from .ai.providers import get_provider, AIProviderError
from .ai.streaming import StreamingResponse, StreamEvent, StreamEventType
from .ai.tools import get_tool_registry, ToolRegistry
from .execution.approval_system import ApprovalSystem
from .ui.terminal_ui import TerminalUI


class AgentState(str, Enum):
    """Agent execution states."""
    IDLE = "idle"
    THINKING = "thinking"
    EXECUTING = "executing"
    WAITING_APPROVAL = "waiting_approval"
    STREAMING = "streaming"
    ERROR = "error"


@dataclass
class AgentContext:
    """Context for agent execution."""
    session: Session
    config: Any
    ui: TerminalUI
    tool_registry: ToolRegistry
    approval_system: ApprovalSystem
    current_state: AgentState = AgentState.IDLE
    pending_tool_calls: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.pending_tool_calls is None:
            self.pending_tool_calls = []


class AgentEngine:
    """
    Central orchestrator for the AI-powered agent system.
    
    Manages the complete workflow from user input to AI response to tool execution,
    with streaming, approval systems, and comprehensive error handling.
    """
    
    def __init__(self, session: Session):
        """
        Initialize the agent engine.
        
        Args:
            session: Session instance for conversation management
        """
        self.session = session
        self.config = get_config()
        self.console = Console()
        
        # Initialize components
        self.ui = TerminalUI()
        self.tool_registry = get_tool_registry()
        self.approval_system = ApprovalSystem()
        
        # Agent context
        self.context = AgentContext(
            session=session,
            config=self.config,
            ui=self.ui,
            tool_registry=self.tool_registry,
            approval_system=self.approval_system
        )
        
        # AI provider
        self.ai_provider = None
        self._initialize_ai_provider()
    
    def _initialize_ai_provider(self) -> None:
        """Initialize AI provider."""
        try:
            self.ai_provider = get_provider(self.config.default_provider)
        except AIProviderError as e:
            self.console.print(f"[red]Failed to initialize AI provider: {e}[/red]")
            raise
    
    async def process_user_input(self, user_input: str) -> None:
        """
        Process user input through the complete AI workflow.
        
        Args:
            user_input: User's input message
        """
        try:
            # Update state
            self.context.current_state = AgentState.THINKING
            
            # Add user message to session
            self.session.add_user_message(user_input)
            
            # Show thinking indicator
            with self.ui.show_thinking():
                # Get conversation for AI
                messages = self.session.get_conversation_for_api(
                    max_tokens=self.config.max_context_length
                )
                
                # Get available tools
                tool_schemas = self.tool_registry.get_openai_schemas()
                
                # Create streaming response
                self.context.current_state = AgentState.STREAMING
                
                streaming_response = await self.ai_provider.stream_chat_completion(
                    messages=messages,
                    model=self.config.default_model,
                    tools=tool_schemas if tool_schemas else None,
                    tool_choice="auto" if tool_schemas else None
                )
                
                # Process streaming response
                await self._process_streaming_response(streaming_response)
        
        except Exception as e:
            self.context.current_state = AgentState.ERROR
            await self._handle_error(e)
    
    async def _process_streaming_response(self, streaming_response: StreamingResponse) -> None:
        """
        Process streaming AI response with real-time updates.
        
        Args:
            streaming_response: Streaming response from AI provider
        """
        content_buffer = ""
        tool_calls = []
        
        # Set up event handlers
        streaming_response.on_content(self._on_content_chunk)
        streaming_response.on_tool_call(self._on_tool_call)
        streaming_response.on_finish(self._on_stream_finish)
        streaming_response.on_error(self._on_stream_error)
        
        # Show live streaming
        with Live(self._create_streaming_panel(""), refresh_per_second=10) as live:
            async for event in streaming_response.process():
                if event.type == StreamEventType.CONTENT:
                    content_buffer += event.data
                    live.update(self._create_streaming_panel(content_buffer))
                
                elif event.type == StreamEventType.TOOL_CALL:
                    tool_call = event.data
                    if tool_call not in tool_calls:
                        tool_calls.append(tool_call)
                    
                    # Update display with tool call info
                    live.update(self._create_streaming_panel(
                        content_buffer, 
                        tool_calls=tool_calls
                    ))
                
                elif event.type == StreamEventType.FINISH:
                    # Final update
                    live.update(self._create_streaming_panel(
                        content_buffer,
                        tool_calls=tool_calls,
                        finished=True
                    ))
                    break
                
                elif event.type == StreamEventType.ERROR:
                    live.update(self._create_error_panel(str(event.data)))
                    return
        
        # Add assistant message to session
        if content_buffer or tool_calls:
            self.session.add_assistant_message(
                content=content_buffer,
                tool_calls=[self._tool_call_to_dict(tc) for tc in tool_calls] if tool_calls else None
            )
        
        # Execute tool calls if any
        if tool_calls:
            await self._execute_tool_calls(tool_calls)
        
        # Update state
        self.context.current_state = AgentState.IDLE
    
    def _create_streaming_panel(
        self, 
        content: str, 
        tool_calls: Optional[List[Any]] = None,
        finished: bool = False
    ) -> Panel:
        """Create panel for streaming display."""
        text = Text()
        
        # Add AI label
        text.append("🤖 ", style="bold blue")
        text.append("Kritrima AI", style="bold cyan")
        
        if not finished:
            text.append(" ●", style="bold green blink")  # Thinking indicator
        
        text.append("\n\n")
        
        # Add content
        if content:
            text.append(content)
        
        # Add tool calls info
        if tool_calls:
            text.append("\n\n")
            text.append("🔧 Tool calls:", style="bold yellow")
            for tool_call in tool_calls:
                text.append(f"\n  • {tool_call.function['name']}")
        
        return Panel(text, border_style="blue", padding=(1, 2))
    
    def _create_error_panel(self, error_message: str) -> Panel:
        """Create panel for error display."""
        text = Text()
        text.append("❌ Error: ", style="bold red")
        text.append(error_message, style="red")
        
        return Panel(text, border_style="red", padding=(1, 2))
    
    async def _execute_tool_calls(self, tool_calls: List[Any]) -> None:
        """
        Execute tool calls with approval system.
        
        Args:
            tool_calls: List of tool calls to execute
        """
        self.context.current_state = AgentState.EXECUTING
        
        for tool_call in tool_calls:
            try:
                function_name = tool_call.function["name"]
                function_args = tool_call.function["arguments"]
                
                # Parse arguments if they're a string
                if isinstance(function_args, str):
                    try:
                        function_args = json.loads(function_args)
                    except json.JSONDecodeError:
                        self.console.print(f"[red]Invalid JSON in tool arguments: {function_args}[/red]")
                        continue
                
                # Check if approval is needed
                tool = self.tool_registry.get_tool(function_name)
                if not tool:
                    self.console.print(f"[red]Unknown tool: {function_name}[/red]")
                    continue
                
                approved = True
                if tool.schema.requires_approval:
                    self.context.current_state = AgentState.WAITING_APPROVAL
                    approved = await self.approval_system.request_approval(
                        tool_name=function_name,
                        parameters=function_args,
                        tool_schema=tool.schema
                    )
                
                if approved:
                    self.context.current_state = AgentState.EXECUTING
                    
                    # Show execution indicator
                    with self.ui.show_execution(function_name):
                        result = await self.tool_registry.execute_tool(
                            tool_name=function_name,
                            parameters=function_args
                        )
                    
                    # Add tool result to session
                    self.session.add_tool_message(
                        content=json.dumps(result, indent=2),
                        tool_call_id=tool_call.id
                    )
                    
                    # Show result
                    self._display_tool_result(function_name, result)
                else:
                    self.console.print(f"[yellow]Tool execution cancelled: {function_name}[/yellow]")
            
            except Exception as e:
                error_msg = f"Tool execution failed: {str(e)}"
                self.console.print(f"[red]{error_msg}[/red]")
                
                # Add error to session
                self.session.add_tool_message(
                    content=json.dumps({"error": error_msg}),
                    tool_call_id=tool_call.id
                )
        
        self.context.current_state = AgentState.IDLE
    
    def _display_tool_result(self, tool_name: str, result: Dict[str, Any]) -> None:
        """Display tool execution result."""
        if result.get("success"):
            self.console.print(f"[green]✓[/green] {tool_name} completed successfully")
            
            # Show relevant output
            tool_result = result.get("result", {})
            if isinstance(tool_result, dict):
                if "stdout" in tool_result and tool_result["stdout"]:
                    self.console.print(tool_result["stdout"])
                elif "content" in tool_result and tool_result["content"]:
                    # Truncate long content
                    content = tool_result["content"]
                    if len(content) > 500:
                        content = content[:500] + "... (truncated)"
                    self.console.print(content)
        else:
            self.console.print(f"[red]✗[/red] {tool_name} failed")
            error = result.get("error") or result.get("result", {}).get("error")
            if error:
                self.console.print(f"[red]Error: {error}[/red]")
    
    def _tool_call_to_dict(self, tool_call: Any) -> Dict[str, Any]:
        """Convert tool call to dictionary for session storage."""
        return {
            "id": tool_call.id,
            "type": tool_call.type,
            "function": tool_call.function
        }
    
    def _on_content_chunk(self, chunk: str) -> None:
        """Handle content chunk from streaming."""
        # This is handled in the main streaming loop
        pass
    
    def _on_tool_call(self, tool_call: Any) -> None:
        """Handle tool call from streaming."""
        # This is handled in the main streaming loop
        pass
    
    def _on_stream_finish(self, finish_reason: str) -> None:
        """Handle stream completion."""
        if self.config.debug:
            self.console.print(f"[dim]Stream finished: {finish_reason}[/dim]")
    
    def _on_stream_error(self, error: Exception) -> None:
        """Handle stream error."""
        self.console.print(f"[red]Streaming error: {str(error)}[/red]")
    
    async def _handle_error(self, error: Exception) -> None:
        """Handle general errors."""
        self.console.print(f"[red]Agent error: {str(error)}[/red]")
        
        if self.config.debug:
            import traceback
            self.console.print(f"[dim]{traceback.format_exc()}[/dim]")
    
    def get_session_summary(self) -> str:
        """Get a summary of the current session."""
        return self.session.get_summary()
    
    def clear_session(self) -> None:
        """Clear the current session."""
        self.session.clear_messages()
        self.console.print("[dim]Session cleared.[/dim]")
    
    async def shutdown(self) -> None:
        """Shutdown the agent engine."""
        # Save session
        self.session.save()
        
        # Clean up any resources
        self.context.current_state = AgentState.IDLE
